{"name": "loopback-datasource-juggler", "version": "5.2.1", "publishConfig": {"export-tests": true}, "description": "LoopBack DataSource Juggler", "keywords": ["StrongLoop", "LoopBack", "DataSource", "Connector", "Database", "Juggler", "ORM"], "author": "IBM Corp.", "engines": {"node": ">=20"}, "repository": {"type": "git", "url": "https://github.com/loopbackio/loopback-datasource-juggler"}, "main": "index.js", "types": "index.d.ts", "browser": {"depd": "./lib/browser.depd.js"}, "scripts": {"lint": "eslint .", "build": "npm run build-ts-types", "build-ts-types": "tsc -p tsconfig.json --outDir dist", "pretest": "npm run build", "test": "nyc mocha", "posttest": "npm run lint", "reinstall": "(rm -rf node_modules/ || true) && (rm package.lock || true) && npm install"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/node": "^24.0.10", "async-iterators": "^0.2.2", "bson-objectid": "^2.0.4", "eslint": "^9.30.1", "eslint-config-loopback": "^13.1.0", "eslint-plugin-mocha": "^11.1.0", "loopback-connector-throwing": "file:./test/fixtures/loopback-connector-throwing", "mocha": "^11.7.1", "nyc": "^17.1.0", "should": "^13.2.3", "sinon": "^21.0.0", "typescript": "^5.8.3"}, "dependencies": {"async": "^3.2.6", "change-case": "5.4.4", "debug": "^4.4.1", "depd": "^2.0.0", "inflection": "^3.0.2", "lodash": "^4.17.21", "loopback-connector": "^6.2.5", "minimatch": "^10.0.3", "nanoid": "3.3.8", "neotraverse": "^0.6.18", "qs": "^6.14.0", "strong-globalize": "^6.0.6", "uuid": "^11.1.0"}, "license": "MIT", "packageManager": "yarn@4.9.2+sha512.1fc009bc09d13cfd0e19efa44cbfc2b9cf6ca61482725eb35bbc5e257e093ebf4130db6dfe15d604ff4b79efd8e1e8e99b25fa7d0a6197c9f9826358d4d65c3c"}