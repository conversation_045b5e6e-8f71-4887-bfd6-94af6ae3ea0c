### Contributing ###

Thank you for your interest in `loopback-datasource-juggler`, an open source project
administered by StrongLoop.

Contributing to `loopback-datasource-juggler` is easy. In a few simple steps:

  * Ensure that your effort is aligned with the project's roadmap by
    talking to the maintainers, especially if you are going to spend a
    lot of time on it.

  * Make something better or fix a bug.

  * If possible update existing or add a new unit test in the [test](tests/) directory

  * Adhere to code style outlined in the [Google C++ Style Guide][] and
    [Google Javascript Style Guide][].

  * Run lint to check adherence the style guide. Note this is also run at the end of the tests.
    ```
    npm run lint
    ```

  * Run tests and verify that they pass, or any that fail already failed before you introduced your change.
    ```
    npm run test
    ```

  * Sign the [Contributor License Agreement](https://cla.strongloop.com/agreements/strongloop/loopback-datasource-juggler)

  * Submit a pull request through Github.


### Contributor License Agreement ###

```
  Individual Contributor License Agreement

  By signing this Individual Contributor License Agreement
  ("Agreement"), and making a Contribution (as defined below) to
  StrongLoop, Inc. ("StrongLoop"), You (as defined below) accept and
  agree to the following terms and conditions for Your present and
  future Contributions submitted to StrongLoop. Except for the license
  granted in this Agreement to StrongLoop and recipients of software
  distributed by StrongLoop, You reserve all right, title, and interest
  in and to Your Contributions.

  1. Definitions

     "You" or "Your" shall mean the copyright owner or the individual
     authorized by the copyright owner that is entering into this
     Agreement with StrongLoop.

     "Contribution" shall mean any original work of authorship,
     including any modifications or additions to an existing work, that
     is intentionally submitted by You to StrongLoop for inclusion in,
     or documentation of, any of the products owned or managed by
     StrongLoop ("Work"). For purposes of this definition, "submitted"
     means any form of electronic, verbal, or written communication
     sent to StrongLoop or its representatives, including but not
     limited to communication or electronic mailing lists, source code
     control systems, and issue tracking systems that are managed by,
     or on behalf of, StrongLoop for the purpose of discussing and
     improving the Work, but excluding communication that is
     conspicuously marked or otherwise designated in writing by You as
     "Not a Contribution."

  2. You Grant a Copyright License to StrongLoop

     Subject to the terms and conditions of this Agreement, You hereby
     grant to StrongLoop and recipients of software distributed by
     StrongLoop, a perpetual, worldwide, non-exclusive, no-charge,
     royalty-free, irrevocable copyright license to reproduce, prepare
     derivative works of, publicly display, publicly perform,
     sublicense, and distribute Your Contributions and such derivative
     works under any license and without any restrictions.

  3. You Grant a Patent License to StrongLoop

     Subject to the terms and conditions of this Agreement, You hereby
     grant to StrongLoop and to recipients of software distributed by
     StrongLoop a perpetual, worldwide, non-exclusive, no-charge,
     royalty-free, irrevocable (except as stated in this Section)
     patent license to make, have made, use, offer to sell, sell,
     import, and otherwise transfer the Work under any license and
     without any restrictions. The patent license You grant to
     StrongLoop under this Section applies only to those patent claims
     licensable by You that are necessarily infringed by Your
     Contributions(s) alone or by combination of Your Contributions(s)
     with the Work to which such Contribution(s) was submitted. If any
     entity institutes a patent litigation against You or any other
     entity (including a cross-claim or counterclaim in a lawsuit)
     alleging that Your Contribution, or the Work to which You have
     contributed, constitutes direct or contributory patent
     infringement, any patent licenses granted to that entity under
     this Agreement for that Contribution or Work shall terminate as
     of the date such litigation is filed.

  4. You Have the Right to Grant Licenses to StrongLoop

     You represent that You are legally entitled to grant the licenses
     in this Agreement.

     If Your employer(s) has rights to intellectual property that You
     create, You represent that You have received permission to make
     the Contributions on behalf of that employer, that Your employer
     has waived such rights for Your Contributions, or that Your
     employer has executed a separate Corporate Contributor License
     Agreement with StrongLoop.

  5. The Contributions Are Your Original Work

     You represent that each of Your Contributions are Your original
     works of authorship (see Section 8 (Submissions on Behalf of
     Others) for submission on behalf of others). You represent that to
     Your knowledge, no other person claims, or has the right to claim,
     any right in any intellectual property right related to Your
     Contributions.

     You also represent that You are not legally obligated, whether by
     entering into an agreement or otherwise, in any way that conflicts
     with the terms of this Agreement.

     You represent that Your Contribution submissions include complete
     details of any third-party license or other restriction (including,
     but not limited to, related patents and trademarks) of which You
     are personally aware and which are associated with any part of
     Your Contributions.

  6. You Don't Have an Obligation to Provide Support for Your Contributions

     You are not expected to provide support for Your Contributions,
     except to the extent You desire to provide support. You may provide
     support for free, for a fee, or not at all.

  6. No Warranties or Conditions

     StrongLoop acknowledges that unless required by applicable law or
     agreed to in writing, You provide Your Contributions on an "AS IS"
     BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, EITHER
     EXPRESS OR IMPLIED, INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES
     OR CONDITIONS OF TITLE, NON-INFRINGEMENT, MERCHANTABILITY, OR
     FITNESS FOR A PARTICULAR PURPOSE.

  7. Submission on Behalf of Others

     If You wish to submit work that is not Your original creation, You
     may submit it to StrongLoop separately from any Contribution,
     identifying the complete details of its source and of any license
     or other restriction (including, but not limited to, related
     patents, trademarks, and license agreements) of which You are
     personally aware, and conspicuously marking the work as
     "Submitted on Behalf of a Third-Party: [named here]".

  8. Agree to Notify of Change of Circumstances

     You agree to notify StrongLoop of any facts or circumstances of
     which You become aware that would make these representations
     inaccurate in any respect. Email <NAME_EMAIL>.
```

[Google C++ Style Guide]: https://google.github.io/styleguide/cppguide.html
[Google Javascript Style Guide]: https://google.github.io/styleguide/javascriptguide.xml
