branches:
  only:
    - master
arch:
  - arm64
  - ppc64le
  - s390x
dist: bionic
language: node_js
before_install: |
  NODEJS_VERSION=$(node --version)
  if [ 'v10' = ${NODEJS_VERSION%%.*} ]
  then
    npm install --global npm@7
  else
    npm install --global npm@8
  fi
script:
  - npm run --ignore-scripts build
  - npm test --ignore-scripts
node_js:
  - 10.24.1
  - 12.22.12
  - 14.21.3
  - 16.20.2
  - 17.9.1
