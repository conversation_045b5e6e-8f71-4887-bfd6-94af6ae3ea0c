{"0483a77cf77741504204e5c066597487": "Relação {{polymorphic}} de {0}: {1} não espera o parâmetro 'polymorphic.as' ao definir 'foreignKey'/'discriminator' customizado ", "09483e03b91c8bd58732a74b3ef1ec13": "Data inválida: {0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany recebeu destino que não contém \"{0}\" necessário", "0b16d3ffc42f91b4b9a4b3b50c41c838": "A ordem {0} não é válida", "0bd753a8944ad0af85a939bb25273887": "Não é possível expirar chave desconhecida {0}", "0c0b867aca0973ba26e887d3337cc4ec": "Modelo de {{Polymorphic}} não localizado: `{0}` não configurado", "0c4eb8b6c2ff6e51d7e195eee346ced9": "A tabela '{0}' não existe.", "0ff31abb394afb555df162e74ff1a0a0": "{{id}} não pode ser atualizado de {0} para {1} quando {{forceId}} é configurado como true", "1ae7d3e0be381efb32bfd1ba652f5172": "AVISO: relação {{polymorphic}} de {0}: {1} usa a palavra-chave 'polymorphic.as', que será DESCONTINUADA em LoopBack.next; consulte este doc para obter soluções de substituição (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "Transações aninhadas não são suportadas", "21095484501dbff31af6556fa6039182": "O parâmetro {{offset/skip}} {0} não é válido", "280f4550f90e133118955ec6f6f72830": "Tipo de discriminador {0} especificado, mas não existe nenhum modelo com esse nome", "28697ec15968a7969211f6d035ba9260": "Relação {{polymorphic}} de {0}: {1} não espera o parâmetro 'model'", "2c4904377a87fdab502118719cc0d266": "{{Transaction}} não é suportada", "2c5c8519721f749aab13c2f04f41d611": "A propriedade {0} possui clá<PERSON>ula inválida {1}: esperado exatamente 2 valores, recebidos {2}", "2f4af31c144bbfab1bbf479866acd820": "\nAVISO: conector {{<PERSON><PERSON><PERSON>}} \"{0}\" não foi instalado como qualquer um dos módulos a seguir:\n\n {1}\n\nPara corrigir, execute:\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "Aviso: o modelo {0}, {{strict mode: `throw`}} foi removido, use {{`strict: true`}} no lugar, que retorna {{`Validation Error`}} para as propriedades desconhecidas,", "38dbf42c29a4645238cc3d632e88ebc9": "{{Relation.modelTo}} não foi definido para a relação {0} e não é {{polymorphic}}", "3cde8cc9bca22c67278b202ab0720106": "Nenhuma instância com ID {0} localizada para {1}", "416dfbb7b823f51c9f3800be81060b41": "Nenhuma instância com {{id}} {0} localizada para {1}", "49b5afd8c6a19ad9c8abeffb2f8114eb": "<PERSON> método Belong<PERSON>o \"getAsync()\" está descontinuado, use \"get()\" em vez disso.", "4c78325cedbb826db3a05bf5df0e8546": "<PERSON><PERSON>-se fornecer um {{id}} ao substituir!", "4e31b1edd10dadb724d83387de0b5062": "O parâmetro {0} de {{limit}} não é válido", "514985b2327f061ffb1c932f6b909979": "O modelo {0} não está definido.", "525c856e65daab43be247e7b5410febd": "Relação {{polymorphic}} de {0}: {1} não espera o parâmetro 'polymorphic.selector' ao definir 'foreignKey'/'discriminator' customizado ", "5c18ee111dd87540cdb19a2a93b33be9": "A transação é recuperada devido à transcorrência do tempo limite", "5ec7e6664256f7ea78f4f06dafc7d974": "A transação não está pronta, aguarde a promessa retornada para resolver", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "Nenhum nome de {{id}} {0}", "614e3355647e4127c96256102dc63376": "A propriedade {0} possui cláusula inválida {1}: esperada uma sequência ou um RegExp", "62a2d80c405b7fec5f547c448ab1b6ff": "A {{order}} {0} possui direção inválida", "6502a117987610380b9068ef98b1b0ee": "Nenhum registro encontrado em {0} para ({1}.{2} ,{3}.{4})", "67c2bf43b5281ab929617423ea8a6f3e": "O conector {0} não suporta operação {{replaceById}}. Este não é um erro no LoopBack. Entre em contato com os autores do conector, de preferência via problemas do GitHub.", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}} requer o argumento {{id}}", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}} requer o argumento {{id}}", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "A relação {{HasOne}} não pode criar mais de uma instância de {0}", "728232e473bf80272c042df2b7e002f4": "Relação {{polymorphic}} de {0}: {1} requer o parâmetro 'polymorphic.discriminator' quando o parâmetro 'polymorphic.foreignKey' é fornecido", "791ab3031a73ede03f7d6299a85e8289": "Tempo limite na conexão após {0} ms", "7b277018e43d41bc445731092b91547d": "Não está conectado", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}} requer o argumento {{id}}", "7e9530c0399289be0ee601a604be71ff": "Relação {{BelongsTo}} {0} está vazia", "7faa840eb6ce11250a141deb42a6c489": "{{scope}} da relação desconhecido: {0}", "8091838319a5cc7d6a34af2f2a616ce9": "O nome da propriedade não deve ser \"{{constructor}}\" no Modelo: {0}", "881e4b0cb86ed59549248ee540a9fd10": "O nome da propriedade \"{{constructor}}\" não é permitido nos dados de {0}", "89afd3a9249f5a8d3edda07d84ca049d": "Modelo de {{Polymorphic}} não localizado: `{0}`", "89bf6d92731fe7bd2146ce8d0bec205c": "Argumento inválido, deve ser uma sequência, literal {{regex}} ou objeto {{RegExp}}", "8a39126103a157f501affa070367a1b0": "A instância de {0} não é válida. Detalhes: {1}.", "8c5ab01638c1ac1d58168c6346a8481a": "Sinalizações de {{regex}} inválidas: {0}", "938401ea4ce48159efa9be1d4a5e8bab": "Itens devem ser uma matriz: {0}", "9e1f143ee02946324d34da92f71bf74e": "Relação de {0}: {1} requer o parâmetro 'model'", "a004f310d315e592843776fab964eaeb": "Re<PERSON><PERSON><PERSON><PERSON> de {{Polymorphic}} precisam de um modelo completo", "a0cf0e09c26df14283223e84e6a10f00": "Não foi possível atualizar atributos. {{Object}} com {{id}} {0} não existe!", "a2487abefef4259c2131d96cdb8543b1": "Falha de conexão: {0}\nEla será tentada novamente para a próxima solicitação.", "a25e41a39c60c4702e55d0c3936576a1": "Incompatibilidade de chaves: {0}.{1}: {2}, {3}.{4}: {5}", "a327355560d495454fba2c1aad6bdf09": "Método de escopo desconhecido: {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "Execute o comando \"{{npm install loopback-datasource-juggler}} {0}\" ", "a829dee089c912e68c18920ba015400c": "AVISO: a propriedade {{id}} não pode ser mudada de {0} para {1} para o modelo:{2} no gancho de operação {{'loaded'}}", "a984a076c59e451948b2bcf7a393d860": "AVISO: a propriedade {{id}} não pode ser mudada de {0} para {1} para o modelo:{2} no gancho de operação {{'before save'}}", "ac04cf275b71c1eb89a41cf6bbad7a64": "<PERSON> mé<PERSON>do <PERSON> \"getAsync()\" está descontinuado, use \"get()\" em vez disso.", "b138294f132edfe1eb2a8211150c7238": "`Indefinido` inesperado na consulta", "b15b20280211ad258d92947f05b6e4a5": "O conector não foi inicializado.", "b278876ec93ef9760f00e83f38ba313d": "<PERSON> <PERSON><PERSON><PERSON><PERSON> \"getAsync()\" está descontinuado, use \"find()\" em vez disso.", "ba0fd8106eb54de4d003a844206431fd": "O gancho de modelo \"{0}\" está descontinuado, use ganchos de Operação no lugar. {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "A cláusula where {0} não é um {{object}}", "bdb11cc1c780c9ccac33c316cfdc9d82": "Tipo não definido para a propriedade {0}.{1}", "bdfb951c8ff7ce0cbc08c06f548fd927": "O valor é um {{object}} vazio", "bec226891a505828bfc76c5cfd73b336": "Não é possível obter TTL para chave desconhecida {0}", "cd930369e86cdd222f7bd117c6f9fa94": "Provedor do valor padrão {0} desconhecido", "cfee4d8149316d9a647c0885cf3cafaf": "Nomes da propriedade contendo pontos não são suportados. Modelo: {0}, propriedade dinâmica: {1}", "d40328eabd8756d795bcdd49d782d4e9": "A Origem de Dados não suporta transações", "da02dd6c53d4148320eeb31718a7aebe": "<PERSON>ipo inv<PERSON>o para a propriedade {0}", "da751a8a748adbde5b55fa83b707b4e2": "Nomes da propriedade contendo pontos não são suportados. Modelo: {0}, propriedade: {1}", "db03083e9a768388fdbee865249ac67a": "Ignorando erros de validação em {{updateOrCreate()}}:", "dd63416d9b7d9fa4181e89efd619dfd8": "O valor não é um {{array}} ou {{object}} com índices numéricos sequenciais", "ddf0aa14803f1c84f4a97f3803f7471c": "Nome de classe necessário", "e08ab0e1ab55f26c357061447b635905": "Nenhuma relação encontrada em {0} para ({1}.{2},{3}.{4})", "e0e9504e137a3c3339144b51ed76fef2": "O conector não foi definido corretamente: ele deve criar um membro de `{{connector}}` igual a dataSource", "e2f282cbe3efba001d6d3a09f7f6ca8c": "Relação {{polymorphic}} de {0}: {1} requer o parâmetro 'polymorphic.foreignKey' quando o parâmetro 'polymorphic.discriminator' é fornecido", "e39e0f5d52bfbf511e645d19ecadd2fa": "A propriedade {0} poss<PERSON> cl<PERSON>ula inválida {1}: {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "\"{0}\" {{id}} \"{1}\" desconhecido.", "e54d944c2a2c85a23caa86027ae307cf": "Não é possível migrar modelos não conectados a esta origem de dados: {0}", "e54f118a75e15e132f16b985274eb46d": "O filtro de consulta {0} não é um {{object}}", "e55937649d8d7a11706b8cec22d02eae": "Relação {{Has<PERSON>ne}} {0} está vazia", "e6161ae8459c79d810e2aa9d21282a39": "<PERSON><PERSON>-se fornecer um {{id}} ao atualizar atributos!", "eb56c2b0c30cf006e2df00a549ec9c2c": "A relação \"{0}\" não foi definida para o modelo {1}", "ec42dca074f1818c447f7ad16e2d01af": "{0} não é fornecido pelo conector conectado", "ecb7aa804bf54c682999d20d6436104c": "A {{transaction}} não está ativa: {0}", "f30809cb932b72a66416a709c8531530": "O conector não suporta {{method}} dentro de uma transação", "f41bd91dc0f000a79c0bf842f1b7fdf9": "não foi possível criar Lista a partir da sequência JSON: {0}", "f6e8c96c93b9c7687d6c172b3695e898": "A propriedade de {{id}} ({0}) não pode ser atualizada de {1} para {2}", "fa9ae17e8e008d0eb0f0421a2972308c": "Relação {{polymorphic}} de {0}: {1} requer o parâmetro 'model'", "fca4d12faff1035d9d0438d73432571b": "Entrada suplicada para {0}.{1}", "fd3cc89dc67e2d604eaae21bdf41d403": "Não foi possível localizar a relação {0} para o modelo {1}", "fec8ebda24db46a9d040bf863765cc44": "O operador {0} p<PERSON><PERSON> cl<PERSON> inválid<PERSON> {1}: {2}"}