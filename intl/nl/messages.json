{"0483a77cf77741504204e5c066597487": "{{polymorphic}} {0} relatie: {1} verwacht niet dat de parameter `polymorphic.as` wordt gebruikt bij het definiëren van een aangepaste `foreignKey`/`discriminator` ", "09483e03b91c8bd58732a74b3ef1ec13": "Ongeldige datum: {0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany heeft een doel ontvangen dat niet de vereiste \"{0}\" bevat", "0b16d3ffc42f91b4b9a4b3b50c41c838": "De volgorde {0} is niet geldig", "0bd753a8944ad0af85a939bb25273887": "<PERSON><PERSON><PERSON><PERSON> sleutel {0} kan niet vervallen", "0c0b867aca0973ba26e887d3337cc4ec": "Model {{Polymorphic}} is niet gevonden: '{0}' niet ingesteld", "0c4eb8b6c2ff6e51d7e195eee346ced9": "Ta<PERSON> '{0}' bestaat niet.", "0ff31abb394afb555df162e74ff1a0a0": "{{id}} kan niet worden bijgewerkt van {0} naar {1} zolang {{forceId}} is ingesteld op true", "1ae7d3e0be381efb32bfd1ba652f5172": "WAARSCHUWING: {{polymorphic}} {0} relatie: {1} gebruikt trefwoord `polymorphic.as`. Dit wordt in LoopBack.next gedeprecieerd. Raadpleeg dit document voor vervangende oplossingen: (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "Het nesten van transacties wordt niet ondersteund", "21095484501dbff31af6556fa6039182": "De {{offset/skip}}-parameter {0} is niet geldig", "280f4550f90e133118955ec6f6f72830": "Discriminatortype {0} op<PERSON><PERSON><PERSON>, maar er bestaat geen model met deze naam", "28697ec15968a7969211f6d035ba9260": "{{polymorphic}} {0} relatie: {1} verwacht niet dat de parameter `model` wordt gebruikt", "2c4904377a87fdab502118719cc0d266": "{{Transaction}} wordt niet ondersteund", "2c5c8519721f749aab13c2f04f41d611": "De eigenschap {0} heeft een ongeldige clausule {1}: <PERSON><PERSON> werden precies twee waarden verwacht. Ontvangen: {2}", "2f4af31c144bbfab1bbf479866acd820": "\nWaarschuwing: {{LoopBack}}-connector \"{0}\" is niet g<PERSON><PERSON><PERSON><PERSON><PERSON> als een van de volgende modules:\n\n {1}\n\nU lost dit op door het uitvoeren van:\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "Waarschuwing: Model {0}, {{strict mode: `throw`}} is verwijderd. Gebruik in plaats hiervan {{`strict: true`}}; hiermee wordt {{`Validation Error`}} gemeld voor onbekende eigenschappen.", "********************************": "{{Relation.modelTo}} is niet gedefinieerd voor relatie {0} en is geen {{polymorphic}}", "3cde8cc9bca22c67278b202ab0720106": "<PERSON>n instance met ID {0} gevonden voor {1}", "416dfbb7b823f51c9f3800be81060b41": "Geen instance met {{id}} {0} gevonden voor {1}", "49b5afd8c6a19ad9c8abeffb2f8114eb": "BelongsTo-methode \"getAsync()\" is gedeprecieerd. In plaats daarvan moet u \"get()\" gebruiken.", "4c78325cedbb826db3a05bf5df0e8546": "U moet een {{id}} opgeven bij een vervanging.", "4e31b1edd10dadb724d83387de0b5062": "De {{limit}}-parameter {0} is niet geldig", "514985b2327f061ffb1c932f6b909979": "Model {0} is niet gede<PERSON>.", "525c856e65daab43be247e7b5410febd": "{{polymorphic}} {0} relatie: {1} verwacht niet dat de parameter `polymorphic.selector` wordt gebruikt bij het definiëren van een aangepaste `foreignKey`/`discriminator` ", "5c18ee111dd87540cdb19a2a93b33be9": "De transactie is teruggedra<PERSON> vanwege een timeout", "5ec7e6664256f7ea78f4f06dafc7d974": "De transactie is niet gereed, wacht tot de geretourneerde promise omgezet is", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "Geen {{id}}-naam {0}", "614e3355647e4127c96256102dc63376": "De eigenschap {0} heeft een ongeldige clausule {1}: Er werd een tekenreeks of expressie verwacht", "62a2d80c405b7fec5f547c448ab1b6ff": "De {{order}} {0} heeft een ongeldige richting", "6502a117987610380b9068ef98b1b0ee": "<PERSON>n record gevonden in {0} voor ({1}.{2} ,{3}.{4})", "67c2bf43b5281ab929617423ea8a6f3e": "De connector {0} biedt geen ondersteuning voor de bewerking {{replaceById}}. Dit is geen programmafout in LoopBack. Neem contact op met de auteurs van de connector, bij voorkeur via GitHub-problemen.", "6c3234937d69763fc7f6bcafccc59bbc": "Voor {{Model::deleteById}} is het argument {{id}} vereist", "6eb6fd4fbd73394000bc25f5776fd20c": "Voor {{Model::exists}} is het argument {{id}} vereist", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "{{<PERSON><PERSON><PERSON>}}-relatie kan niet resulteren in meer dan één instance van {0}", "728232e473bf80272c042df2b7e002f4": "{{polymorphic}} {0} relatie: als de parameter `polymorphic.foreignKey` wordt opgegeven, vereist {1} de parameter `polymorphic.discriminator`", "791ab3031a73ede03f7d6299a85e8289": "Timeout voor het maken van verbinding na {0} ms", "7b277018e43d41bc445731092b91547d": "<PERSON><PERSON> verbinding", "7bbbdece4eea90e42aa5c0bce295e503": "Voor {{Model::findById}} is het argument {{id}} vereist", "7e9530c0399289be0ee601a604be71ff": "{{BelongsTo}}-relatie {0} is leeg", "7faa840eb6ce11250a141deb42a6c489": "Onbekende relatie {{scope}}: {0}", "8091838319a5cc7d6a34af2f2a616ce9": "Eigenschapnaam mag niet \"{{constructor}}\" zijn in model: {0}", "881e4b0cb86ed59549248ee540a9fd10": "Eigenschapnaam \"{{constructor}}\" is niet <PERSON><PERSON><PERSON><PERSON> in {0}-gegevens", "89afd3a9249f5a8d3edda07d84ca049d": "{{Polymorphic}}-model is niet gevonden: '{0}'", "89bf6d92731fe7bd2146ce8d0bec205c": "Ongeldig argument, moet een tekenree<PERSON>, {{regex}}-literaal of {{RegExp}}-object zijn", "8a39126103a157f501affa070367a1b0": "De instance {0} is niet geldig. Details: {1}.", "8c5ab01638c1ac1d58168c6346a8481a": "Ongeldige {{regex}}-vlaggen: {0}", "938401ea4ce48159efa9be1d4a5e8bab": "Items moeten een array zijn: {0}", "9e1f143ee02946324d34da92f71bf74e": "{0} relatie: {1} vereist de parameter `model`", "a004f310d315e592843776fab964eaeb": "{{Polymorphic}}-relaties hebben een doorvoermodel nodig", "a0cf0e09c26df14283223e84e6a10f00": "De kenmerken kunnen niet worden bijgewerkt. {{Object}} met {{id}} {0} bestaat niet!", "a2487abefef4259c2131d96cdb8543b1": "Verbinding mislukt: {0}\nVerbindingspoging wordt herhaald bij volgende opdracht.", "a25e41a39c60c4702e55d0c3936576a1": "Niet-overeenkomende sleutels: {0}.{1}: {2}, {3}.{4}: {5}", "a327355560d495454fba2c1aad6bdf09": "Onbekende methode voor bereik: {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "<PERSON><PERSON><PERSON> de opdracht \"{{npm install loopback-datasource-juggler}} {0}\" uit", "a829dee089c912e68c18920ba015400c": "Waarschuwing: Eigenschap {{id}} kan niet worden gewij<PERSON>d van {0} in {1} voor model:{2} in operation hook {{'loaded'}}", "a984a076c59e451948b2bcf7a393d860": "Waarschuwing: Eigenschap {{id}} kan niet worden gewij<PERSON>d van {0} in {1} voor model:{2} in operation hook {{'before save'}}", "ac04cf275b71c1eb89a41cf6bbad7a64": "HasOne-methode \"getAsync()\" is gedeprecieerd. In plaats daarvan moet u \"get()\" gebruiken.", "b138294f132edfe1eb2a8211150c7238": "Onverwacht item 'undefined' in query", "b15b20280211ad258d92947f05b6e4a5": "De connector is niet ge<PERSON><PERSON>tial<PERSON>.", "b278876ec93ef9760f00e83f38ba313d": "Scope-methode \"getAsync()\" is gedeprecieerd. In plaats daarvan moet u \"find()\" gebruiken.", "ba0fd8106eb54de4d003a844206431fd": "Model hook \"{0}\" is gedeprecieerd; gebruik in plaats daarvan operation hooks. {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "De WHERE-clausule {0} is geen {{object}}", "bdb11cc1c780c9ccac33c316cfdc9d82": "Type niet gedefinieerd voor eigenschap {0}.{1}", "bdfb951c8ff7ce0cbc08c06f548fd927": "Waarde is een leeg {{object}}", "bec226891a505828bfc76c5cfd73b336": "TTL voor onbekende sleutel {0} kan niet worden opgehaald", "cd930369e86cdd222f7bd117c6f9fa94": "Onbekende standaardwaarde voor provider {0}", "cfee4d8149316d9a647c0885cf3cafaf": "Eigenschapnamen met punt(en) worden niet ondersteund. Model: {0}, dynamische eigenschap: {1}", "d40328eabd8756d795bcdd49d782d4e9": "DataSource ondersteunt geen transacties", "da02dd6c53d4148320eeb31718a7aebe": "Ongeldig type voor eigenschap {0}", "da751a8a748adbde5b55fa83b707b4e2": "Eigenschapnamen met punt(en) worden niet ondersteund. Model: {0}, eigenschap: {1}", "db03083e9a768388fdbee865249ac67a": "Validatiefouten in {{updateOrCreate()}} worden genegeerd:", "dd63416d9b7d9fa4181e89efd619dfd8": "<PERSON>aar<PERSON> is geen {{array}} of {{object}} met sequentiële numerieke indices", "ddf0aa14803f1c84f4a97f3803f7471c": "Klassennaam vereist", "e08ab0e1ab55f26c357061447b635905": "<PERSON><PERSON> relatie gevonden in in {0} voor ({1}.{2},{3}.{4})", "e0e9504e137a3c3339144b51ed76fef2": "Connector is niet juist gedefinieerd: moet '{{connector}}'-<PERSON> van gegevensbron maken", "e2f282cbe3efba001d6d3a09f7f6ca8c": "{{polymorphic}} {0} relatie: als de parameter `polymorphic.discriminator` wordt opgegeven, vereist {1} de parameter `polymorphic.foreignKey`", "e39e0f5d52bfbf511e645d19ecadd2fa": "De eigenschap {0} heeft een ongeldige clausule {1}: {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "Onbekend \"{0}\" {{id}} \"{1}\".", "e54d944c2a2c85a23caa86027ae307cf": "Kan geen modellen migreren die niet zijn gekoppeld aan deze gegevensbron: {0}", "e54f118a75e15e132f16b985274eb46d": "Het queryfilter {0} is geen {{object}}", "e55937649d8d7a11706b8cec22d02eae": "{{<PERSON><PERSON><PERSON>}}-relatie {0} is leeg", "e6161ae8459c79d810e2aa9d21282a39": "U moet een {{id}} opgeven bij het bijwerken van kenmerken.", "eb56c2b0c30cf006e2df00a549ec9c2c": "Relatie \"{0}\" is niet gedefinieerd voor model {1}", "ec42dca074f1818c447f7ad16e2d01af": "{0} is niet opgegeven door gekoppelde connector", "ecb7aa804bf54c682999d20d6436104c": "De {{transaction}} is niet actief: {0}", "f30809cb932b72a66416a709c8531530": "De connector ondersteunt {{method}} niet binnen een transactie", "f41bd91dc0f000a79c0bf842f1b7fdf9": "kan geen lijst maken op basis van JSON-reeks: {0}", "f6e8c96c93b9c7687d6c172b3695e898": "{{id}}-eigenschap ({0}) kan niet worden bijgewerkt van {1} in {2}", "fa9ae17e8e008d0eb0f0421a2972308c": "{{polymorphic}} {0} relatie: {1} vereist de parameter `model`", "fca4d12faff1035d9d0438d73432571b": "Dubbel item voor {0}.{1}", "fd3cc89dc67e2d604eaae21bdf41d403": "Relatie {0} voor model {1} is niet g<PERSON>nden", "fec8ebda24db46a9d040bf863765cc44": "De operator {0} heeft ongeldige clausules {1}: {2}"}