{"0483a77cf77741504204e5c066597487": "{{polymorphic}} {0} 関係: {1} は、カスタムの foreignKey/discriminator`を定義する際は、パラメーター polymorphic.as`を予期していません ", "09483e03b91c8bd58732a74b3ef1ec13": "無効な日付: {0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany は、必須の \"{0}\" が含まれていないターゲットを受信しました", "0b16d3ffc42f91b4b9a4b3b50c41c838": "順序 {0} が無効です", "0bd753a8944ad0af85a939bb25273887": "不明のキー {0} を期限切れにすることができません", "0c0b867aca0973ba26e887d3337cc4ec": "{{Polymorphic}} モデルが見つかりません: `{0}` が設定されていません", "0c4eb8b6c2ff6e51d7e195eee346ced9": "テーブル '{0}' が存在しません。", "0ff31abb394afb555df162e74ff1a0a0": "{{forceId}} が true に設定されている場合、{{id}} を {0} から {1} に更新することはできません", "1ae7d3e0be381efb32bfd1ba652f5172": "警告: {{polymorphic}} {0} 関係: {1} でキーワード`polymorphic.as`が使用されていますが、これは LoopBack.next で非推奨になる予定です。代替解決策について次の文書を参照してください (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "トランザクションのネストはサポートされていません", "21095484501dbff31af6556fa6039182": "{{offset/skip}} パラメーター {0} は無効です", "280f4550f90e133118955ec6f6f72830": "判別プログラム・タイプ {0} が指定されていますが、このような名前のモデルは存在しません", "28697ec15968a7969211f6d035ba9260": "{{polymorphic}} {0} 関係: {1} はパラメーター`model を予期していません`", "2c4904377a87fdab502118719cc0d266": "{{Transaction}} はサポートされていません", "2c5c8519721f749aab13c2f04f41d611": "{0} プロパティーには無効な節 {1} があります。正確に 2 つの値が必要ですが、{2} を受け取りました", "2f4af31c144bbfab1bbf479866acd820": "\n警告: {{LoopBack}} コネクター \"{0}\" は次のいずれのモジュールとしてもインストールされていません:\n\n {1}\n\n修正するには、以下を実行します。\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "警告: モデル {0}、{{strict mode: `throw`}} は削除されました。代わりに {{`strict: true`}} を使用してください。これにより、不明なプロパティーの {{`Validation Error`}} が返されます", "38dbf42c29a4645238cc3d632e88ebc9": "{{Relation.modelTo}} は関係 {0} に定義されておらず、{{polymorphic}} ではありません", "3cde8cc9bca22c67278b202ab0720106": "{1} に関する ID {0} のインスタンスが見つかりません", "416dfbb7b823f51c9f3800be81060b41": "{1} に関する {{id}} {0} のインスタンスが見つかりません", "49b5afd8c6a19ad9c8abeffb2f8114eb": "BelongsTo のメソッド \"getAsync()\" は非推奨です。代わりに \"get()\" を使用してください。", "4c78325cedbb826db3a05bf5df0e8546": "置換するときは {{id}} を指定する必要があります。", "4e31b1edd10dadb724d83387de0b5062": "{{limit}} パラメーター {0} は無効です", "514985b2327f061ffb1c932f6b909979": "モデル {0} が定義されていません。", "525c856e65daab43be247e7b5410febd": "{{polymorphic}} {0} 関係: {1} は、カスタムの foreignKey/discriminator`を定義する際は、パラメーター polymorphic.selector`を予期していません ", "5c18ee111dd87540cdb19a2a93b33be9": "タイムアウトのため、トランザクションがロールバックされます", "5ec7e6664256f7ea78f4f06dafc7d974": "トランザクションの準備ができていません。返された Promise の解決をお待ちください", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "{{id}} 名 {0} がありません", "614e3355647e4127c96256102dc63376": "{0} プロパティーには無効な節 {1} があります。ストリングまたは正規表現を指定する必要があります", "62a2d80c405b7fec5f547c448ab1b6ff": "{{order}} {0} の方向が無効です", "6502a117987610380b9068ef98b1b0ee": "({1}.{2}、{3}.{4}) に関して {0} でレコードが見つかりません", "67c2bf43b5281ab929617423ea8a6f3e": "コネクター {0} では {{replaceById}} 操作はサポートされません。 これは LoopBack のバグではありません。 コネクターの作成者に (なるべく GitHub Issue を通して) 問い合わせてください。", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}} には {{id}} 引数が必要です", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}} には {{id}} 引数が必要です", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "{{HasOne}} 関係では {0} のインスタンスを複数作成することはできません", "728232e473bf80272c042df2b7e002f4": "{{polymorphic}} {0} 関係: {1} では、パラメーター polymorphic.foreignKey`を指定したときは、パラメーター polymorphic.discriminator`は必須です", "791ab3031a73ede03f7d6299a85e8289": "接続は {0} ミリ秒後にタイムアウトになります", "7b277018e43d41bc445731092b91547d": "未接続", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}} には {{id}} 引数が必要です", "7e9530c0399289be0ee601a604be71ff": "{{BelongsTo}} 関係 {0} が空です", "7faa840eb6ce11250a141deb42a6c489": "不明な関係 {{scope}}: {0}", "8091838319a5cc7d6a34af2f2a616ce9": "モデル {0} のプロパティー名を \"{{constructor}}\" にすることはできません", "881e4b0cb86ed59549248ee540a9fd10": "{0} データではプロパティー名 \"{{constructor}}\" は許可されません", "89afd3a9249f5a8d3edda07d84ca049d": "{{Polymorphic}} モデルが見つかりません: `{0}`", "89bf6d92731fe7bd2146ce8d0bec205c": "引数が無効です。ストリング、{{regex}} リテラル、または {{RegExp}} オブジェクトでなければなりません", "8a39126103a157f501affa070367a1b0": "{0} インスタンスは無効です。 詳細: {1}。", "8c5ab01638c1ac1d58168c6346a8481a": "無効な {{regex}} フラグ: {0}", "938401ea4ce48159efa9be1d4a5e8bab": "項目は配列でなければなりません: {0}", "9e1f143ee02946324d34da92f71bf74e": "{0} 関係: {1} ではパラメーター`model は必須です`", "a004f310d315e592843776fab964eaeb": "{{Polymorphic}} 関係にはスルー・モデルが必要です", "a0cf0e09c26df14283223e84e6a10f00": "属性を更新できませんでした。 {{id}} {0} の {{Object}} は存在しません。", "a2487abefef4259c2131d96cdb8543b1": "接続失敗: {0}\n次の要求で再試行されます。", "a25e41a39c60c4702e55d0c3936576a1": "キーの不一致: {0}.{1}: {2}、{3}.{4}: {5}", "a327355560d495454fba2c1aad6bdf09": "不明なスコープ・メソッド: {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "\"{{npm install loopback-datasource-juggler}} {0}\" コマンドを実行します ", "a829dee089c912e68c18920ba015400c": "警告: {{'loaded'}} 操作フックでモデル {2} の {{id}} プロパティーを {0} から {1} に変更することはできません", "a984a076c59e451948b2bcf7a393d860": "警告: {{'before save'}} 操作フックでモデル {2} の {{id}} プロパティーを {0} から {1} に変更することはできません", "ac04cf275b71c1eb89a41cf6bbad7a64": "HasOne のメソッド \"getAsync()\" は非推奨です。代わりに \"get()\" を使用してください。", "b138294f132edfe1eb2a8211150c7238": "照会内に予期しない `undefined` があります", "b15b20280211ad258d92947f05b6e4a5": "コネクターが初期化されていません。", "b278876ec93ef9760f00e83f38ba313d": "Scope のメソッド \"getAsync()\" は非推奨です。代わりに \"find()\" を使用してください。", "ba0fd8106eb54de4d003a844206431fd": "モデル・フック \"{0}\" は非推奨です。代わりに操作フックを使用してください。 {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "where 節 {0} が {{object}} ではありません", "bdb11cc1c780c9ccac33c316cfdc9d82": "プロパティー {0}.{1} にタイプが定義されていません", "bdfb951c8ff7ce0cbc08c06f548fd927": "値は空の {{object}} です", "bec226891a505828bfc76c5cfd73b336": "不明のキー {0} の TTL を取得できません", "cd930369e86cdd222f7bd117c6f9fa94": "不明なデフォルト値プロバイダー {0}", "cfee4d8149316d9a647c0885cf3cafaf": "ドットが含まれたプロパティー名はサポートされていません。 モデル: {0}、動的プロパティー: {1}", "d40328eabd8756d795bcdd49d782d4e9": "データ・ソースがトランザクションをサポートしていません", "da02dd6c53d4148320eeb31718a7aebe": "プロパティー {0} のタイプが無効です", "da751a8a748adbde5b55fa83b707b4e2": "ドットが含まれたプロパティー名はサポートされていません。 モデル: {0}、プロパティー: {1}", "db03083e9a768388fdbee865249ac67a": "{{updateOrCreate()}} での妥当性検査エラーを無視します:", "dd63416d9b7d9fa4181e89efd619dfd8": "値は、連続した数字の索引が含まれた {{array}} または {{object}} ではありません", "ddf0aa14803f1c84f4a97f3803f7471c": "クラス名は必須です", "e08ab0e1ab55f26c357061447b635905": "({1}.{2}、{3}.{4}) に関して {0} で関係が見つかりません", "e0e9504e137a3c3339144b51ed76fef2": "コネクターが正しく定義されていません: dataSource の `{{connector}}` メンバーを作成する必要があります", "e2f282cbe3efba001d6d3a09f7f6ca8c": "{{polymorphic}} {0} 関係: {1} では、パラメーター polymorphic.discriminator`を指定したときは、パラメーター polymorphic.foreignKey`は必須です", "e39e0f5d52bfbf511e645d19ecadd2fa": "{0} プロパティーには無効な節 {1} があります: {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "\"{0}\" {{id}} \"{1}\" が不明です。", "e54d944c2a2c85a23caa86027ae307cf": "このデータ・ソースに付加されていないモデルはマイグレーションできません: {0}", "e54f118a75e15e132f16b985274eb46d": "照会フィルター {0} が {{object}} ではありません", "e55937649d8d7a11706b8cec22d02eae": "{{HasOne}} 関係 {0} が空です", "e6161ae8459c79d810e2aa9d21282a39": "属性を更新するときは {{id}} を指定する必要があります。", "eb56c2b0c30cf006e2df00a549ec9c2c": "関係 \"{0}\" は {1} モデルに定義されていません", "ec42dca074f1818c447f7ad16e2d01af": "{0} は付加されたコネクターによって提供されません", "ecb7aa804bf54c682999d20d6436104c": "{{transaction}} がアクティブではありません: {0}", "f30809cb932b72a66416a709c8531530": "コネクターがトランザクション内の {{method}} をサポートしていません", "f41bd91dc0f000a79c0bf842f1b7fdf9": "JSON ストリングからリストを作成できませんでした: {0}", "f6e8c96c93b9c7687d6c172b3695e898": "{{id}} プロパティー ({0}) を {1} から {2} に更新できません", "fa9ae17e8e008d0eb0f0421a2972308c": "{{polymorphic}} {0} 関係: {1} ではパラメーター`model は必須です`", "fca4d12faff1035d9d0438d73432571b": "{0}.{1} のエントリーが重複しています", "fd3cc89dc67e2d604eaae21bdf41d403": "モデル {1} の関係 {0} が見つかりませんでした", "fec8ebda24db46a9d040bf863765cc44": "{0} 演算子には無効な節 {1} があります: {2}"}