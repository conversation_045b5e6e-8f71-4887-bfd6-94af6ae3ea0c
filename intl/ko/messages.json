{"0483a77cf77741504204e5c066597487": "{{polymorphic}} {0} 관계: 사용자 정의 `foreignKey`/`discriminator` 정의 중에는 {1}에 `polymorphic.as` 매개변수가 필요하지 않음 ", "09483e03b91c8bd58732a74b3ef1ec13": "올바르지 않은 날짜: {0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany에 필수 \"{0}\"이(가) 포함되지 않은 대상이 수신됨", "0b16d3ffc42f91b4b9a4b3b50c41c838": "{0} 순서가 올바르지 않음", "0bd753a8944ad0af85a939bb25273887": "알 수 없는 키 {0}을(를) 만료할 수 없음", "0c0b867aca0973ba26e887d3337cc4ec": "{{Polymorphic}} 모델을 찾을 수 없음: `{0}`이(가) 설정되지 않음", "0c4eb8b6c2ff6e51d7e195eee346ced9": "'{0}' 테이블이 없습니다.", "0ff31abb394afb555df162e74ff1a0a0": "{{forceId}}이(가) true로 설정된 경우 {{id}}을(를) {0}에서 {1}(으)로 업데이트할 수 없음", "1ae7d3e0be381efb32bfd1ba652f5172": "경고: {{polymorphic}} {0} 관계: {1}이(가) LoopBack에서 더 이상 사용되지 않을 `polymorphic.as` 키워드를 사용합니다. 대체 솔루션에 대해서는 다음 문서를 참조하십시오(https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as).", "1daef4e937fe52136597ba8fd2060f55": "중첩 트랜잭션은 지원되지 않음", "21095484501dbff31af6556fa6039182": "{{offset/skip}} 매개변수 {0}이(가) 올바르지 않음", "280f4550f90e133118955ec6f6f72830": "식별자 유형 {0}이(가) 지정되었지만 해당 이름의 모델이 없습니다.", "28697ec15968a7969211f6d035ba9260": "{{polymorphic}} {0} 관계: {1}에는 `model` 매개변수가 필요하지 않음", "2c4904377a87fdab502118719cc0d266": "{{Transaction}}이(가) 지원되지 않음", "2c5c8519721f749aab13c2f04f41d611": "{0} 특성에 올바르지 않은 절 {1}이(가) 있음: 정확하게 2개의 값을 예상했지만 {2}개를 수신함", "2f4af31c144bbfab1bbf479866acd820": "\n경고: {{<PERSON><PERSON><PERSON>}} 커넥터 \"{0}\"이(가) 다음 모듈로 설치되어 있지 않음:\n\n {1}\n\n이를 수정하려면 다음을 실행하십시오. \n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "경고: 모델 {0}, {{strict mode: `throw`}}이(가) 제거되었습니다. 알 수 없는 특성에 대해 {{`Validation Error`}}을(를) 리턴하는 {{`strict: true`}}을(를) 대신 사용하십시오.", "38dbf42c29a4645238cc3d632e88ebc9": "관계 {0}에 대해 {{Relation.modelTo}}이(가) 정의되지 않았으며 {{polymorphic}}이(가) 아닙니다.", "3cde8cc9bca22c67278b202ab0720106": "{1}에 대해 ID {0}의 인스턴스를 찾을 수 없음", "416dfbb7b823f51c9f3800be81060b41": "{1}에 대해 {{id}} {0}의 인스턴스를 찾을 수 없음", "49b5afd8c6a19ad9c8abeffb2f8114eb": "BelongsTo 메소드 \"getAsync()\"는 더 이상 사용되지 않습니다. 대신 \"get()\"을 사용하십시오.", "4c78325cedbb826db3a05bf5df0e8546": "바꾸는 경우 {{id}}을(를) 제공해야 합니다!", "4e31b1edd10dadb724d83387de0b5062": "{{limit}} 매개변수 {0}이(가) 올바르지 않음", "514985b2327f061ffb1c932f6b909979": "{0} 모델이 정의되지 않았습니다.", "525c856e65daab43be247e7b5410febd": "{{polymorphic}} {0} 관계: 사용자 정의 `foreignKey`/`discriminator` 정의 중에는 {1}에 `polymorphic.selector` 매개변수가 필요하지 않음 ", "5c18ee111dd87540cdb19a2a93b33be9": "제한시간 초과로 인해 트랜잭션이 롤백됨", "5ec7e6664256f7ea78f4f06dafc7d974": "트랜잭션이 준비되지 않았으니 리턴된 일정이 해결될 때까지 기다려 주십시오.", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "{{id}} 이름 {0}이(가) 아님", "614e3355647e4127c96256102dc63376": "{0} 특성에 올바르지 않은 절 {1}이(가) 있음: 문자열 또는 RegExp를 예상함", "62a2d80c405b7fec5f547c448ab1b6ff": "{{order}} {0}에 올바르지 않은 방향이 있음", "6502a117987610380b9068ef98b1b0ee": "{0}에서 ({1}.{2} ,{3}.{4})에 대한 레코드를 찾을 수 없음", "67c2bf43b5281ab929617423ea8a6f3e": "커넥터 {0}에서 {{replaceById}} 오퍼레이션을 지원하지 않습니다. 이는 LoopBack의 버그가 아닙니다. 커넥터 작성자에게 문의하십시오. GitHub 발행을 사용하는 것이 좋습니다.", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}}에 {{id}} 인수가 필요함", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}}에 {{id}} 인수가 필요함", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "{{HasOne}} 관계에서 둘 이상의 {0} 인스턴스를 작성할 수 없음", "728232e473bf80272c042df2b7e002f4": "{{polymorphic}} {0} 관계: `polymorphic.foreignKey` 매개변수가 제공된 경우 {1}에는 `polymorphic.discriminator` 매개변수가 필요함", "791ab3031a73ede03f7d6299a85e8289": "{0}밀리초 후 연결 제한시간 초과", "7b277018e43d41bc445731092b91547d": "연결되지 않음", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}}에 {{id}} 인수가 필요함", "7e9530c0399289be0ee601a604be71ff": "{{<PERSON><PERSON><PERSON>To}} 관계 {0}이(가) 비어 있음", "7faa840eb6ce11250a141deb42a6c489": "알 수 없는 관계 {{scope}}: {0}", "8091838319a5cc7d6a34af2f2a616ce9": "모델에서 특성 이름이 \"{{constructor}}\"이어서는 안됨: {0}", "881e4b0cb86ed59549248ee540a9fd10": "{0} 데이터에서는 특성 이름 \"{{constructor}}\"이(가) 허용되지 않음", "89afd3a9249f5a8d3edda07d84ca049d": "{{Polymorphic}} 모델을 찾을 수 없음: `{0}`", "89bf6d92731fe7bd2146ce8d0bec205c": "올바르지 않은 인수가 문자열, {{regex}} 리터럴 또는 {{RegExp}} 오브젝트여야 합니다.", "8a39126103a157f501affa070367a1b0": "{0} 인스턴스가 올바르지 않습니다. 세부사항: {1}.", "8c5ab01638c1ac1d58168c6346a8481a": "올바르지 않은 {{regex}} 플래그: {0}", "938401ea4ce48159efa9be1d4a5e8bab": "항목이 배열이어야 함: {0}", "9e1f143ee02946324d34da92f71bf74e": "{0} 관계: {1}에 `model` 매개변수가 필요함", "a004f310d315e592843776fab964eaeb": "{{Polymorphic}} 관계에는 through 모델이 필요함", "a0cf0e09c26df14283223e84e6a10f00": "속성을 업데이트할 수 없습니다. {{id}} {0}의 {{Object}}이(가) 없습니다!", "a2487abefef4259c2131d96cdb8543b1": "연결 실패: {0}\n다음 요청에서 재시도됩니다.", "a25e41a39c60c4702e55d0c3936576a1": "키 불일치: {0}.{1}: {2}, {3}.{4}: {5}", "a327355560d495454fba2c1aad6bdf09": "알 수 없는 범위 메소드: {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "\"{{npm install loopback-datasource-juggler}} {0}\" 명령 실행 ", "a829dee089c912e68c18920ba015400c": "경고: {{'loaded'}} 오퍼레이션 후크에서 모델:{2}에 대해 {{id}} 특성을 {0}에서 {1}(으)로 변경할 수 없습니다.", "a984a076c59e451948b2bcf7a393d860": "경고: {{'before save'}} 오퍼레이션 후크에서 모델:{2}에 대해 {{id}} 특성을 {0}에서 {1}(으)로 변경할 수 없습니다.", "ac04cf275b71c1eb89a41cf6bbad7a64": "HasOne 메소드 \"getAsync()\"는 더 이상 사용되지 않습니다. 대신 \"get()\"을 사용하십시오.", "b138294f132edfe1eb2a8211150c7238": "조회에서 예상치 못한 `undefined` 항목", "b15b20280211ad258d92947f05b6e4a5": "커넥터가 초기화되지 않았습니다.", "b278876ec93ef9760f00e83f38ba313d": "Scope 메소드 \"getAsync()\"는 더 이상 사용되지 않습니다. 대신 \"find()\"를 사용하십시오.", "ba0fd8106eb54de4d003a844206431fd": "모델 후크 \"{0}\"이(가) 더 이상 사용되지 않습니다. 오퍼레이션 후크가 대신 사용됩니다. {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "where 절 {0}이(가) {{object}}이(가) 아님", "bdb11cc1c780c9ccac33c316cfdc9d82": "특성 {0}.{1}에 유형이 정의되지 않음", "bdfb951c8ff7ce0cbc08c06f548fd927": "값이 빈 {{object}}임", "bec226891a505828bfc76c5cfd73b336": "알 수 없는 키 {0}에 대한 TTL을 가져올 수 없음", "cd930369e86cdd222f7bd117c6f9fa94": "알 수 없는 기본값 제공자 {0}", "cfee4d8149316d9a647c0885cf3cafaf": "점이 포함된 특성 이름은 지원되지 않습니다. 모델: {0}, 동적 특성: {1}", "d40328eabd8756d795bcdd49d782d4e9": "데이터 소스에서 트랜잭션을 지원하지 않음", "da02dd6c53d4148320eeb31718a7aebe": "특성 {0}에 대한 올바르지 않은 유형", "da751a8a748adbde5b55fa83b707b4e2": "점이 포함된 특성 이름은 지원되지 않습니다. 모델: {0}, 특성: {1}", "db03083e9a768388fdbee865249ac67a": "{{updateOrCreate()}}에서 유효성 검증 오류 무시:", "dd63416d9b7d9fa4181e89efd619dfd8": "값이 순차 숫자 색인을 가진 {{array}} 또는 {{object}}이(가) 아님", "ddf0aa14803f1c84f4a97f3803f7471c": "클래스 이름 필수", "e08ab0e1ab55f26c357061447b635905": "{0}에서 ({1}.{2} ,{3}.{4})에 대한 관계를 찾을 수 없음", "e0e9504e137a3c3339144b51ed76fef2": "커넥터가 제대로 정의되지 않음: 데이터 소스의 `{{connector}}` 멤버를 작성해야 합니다.", "e2f282cbe3efba001d6d3a09f7f6ca8c": "{{polymorphic}} {0} 관계: `polymorphic.discriminator` 매개변수가 제공된 경우 {1}에는 `polymorphic.foreignKey` 매개변수가 필요함", "e39e0f5d52bfbf511e645d19ecadd2fa": "{0} 특성에 올바르지 않은 절 {1}이(가) 있음: {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "알 수 없는 \"{0}\" {{id}} \"{1}\".", "e54d944c2a2c85a23caa86027ae307cf": "이 데이터 소스에 첨부되지 않은 모델은 마이그레이션할 수 없음: {0}", "e54f118a75e15e132f16b985274eb46d": "조회 필터 {0}이(가) {{object}}가 아님", "e55937649d8d7a11706b8cec22d02eae": "{{<PERSON>O<PERSON>}} 관계 {0}이(가) 비어 있음", "e6161ae8459c79d810e2aa9d21282a39": "속성을 업데이트하는 경우 {{id}}을(를) 제공해야 합니다!", "eb56c2b0c30cf006e2df00a549ec9c2c": "{1} 모델에 대해 관계 \"{0}\"이(가) 정의되지 않음", "ec42dca074f1818c447f7ad16e2d01af": "첨부된 커넥터에서 {0}을(를) 제공하지 않음", "ecb7aa804bf54c682999d20d6436104c": "{{transaction}}이(가) 활성 상태가 아님: {0}", "f30809cb932b72a66416a709c8531530": "커넥터가 트랜잭션 내에서 {{method}}을(를) 지원하지 않음", "f41bd91dc0f000a79c0bf842f1b7fdf9": "JSON 문자열에서 목록을 작성할 수 없음: {0}", "f6e8c96c93b9c7687d6c172b3695e898": "{{id}} 특성({0})을 {1}에서 {2}(으)로 업데이트할 수 없음", "fa9ae17e8e008d0eb0f0421a2972308c": "{{polymorphic}} {0} 관계: {1}에 `model` 매개변수가 필요함", "fca4d12faff1035d9d0438d73432571b": "{0}.{1}의 중복 항목", "fd3cc89dc67e2d604eaae21bdf41d403": "{1} 모델에 대해 {0} 관계를 찾을 수 없음", "fec8ebda24db46a9d040bf863765cc44": "{0} 연산자에 올바르지 않은 절 {1}이(가) 있음: {2}"}