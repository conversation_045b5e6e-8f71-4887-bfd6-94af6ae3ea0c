{"0483a77cf77741504204e5c066597487": "{{polymorphic}} {0} 关系：定义定制 `foreignKey`/`discriminator` 时，{1} 不期望参数 `polymorphic.as`", "09483e03b91c8bd58732a74b3ef1ec13": "无效日期：{0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany 收到的目标不包含必需的“{0}”", "0b16d3ffc42f91b4b9a4b3b50c41c838": "顺序 {0} 无效", "0bd753a8944ad0af85a939bb25273887": "无法使未知键 {0} 到期", "0c0b867aca0973ba26e887d3337cc4ec": "找不到 {{Polymorphic}} 模型：未设置“{0}”", "0c4eb8b6c2ff6e51d7e195eee346ced9": "表“{0}”不存在。", "0ff31abb394afb555df162e74ff1a0a0": "将 {{forceId}} 设置为 true 时，{{id}} 无法从 {0} 更新为 {1}", "1ae7d3e0be381efb32bfd1ba652f5172": "警告：{{polymorphic}} {0} 关系：{1} 使用关键字 `polymorphic.as`，在 LoopBack.next 中不推荐使用此关键字，请参阅以下文档以了解替代解决方案 (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "不支持嵌套事务", "21095484501dbff31af6556fa6039182": "{{offset/skip}} 参数 {0} 无效", "280f4550f90e133118955ec6f6f72830": "已指定鉴别器类型 {0}，但是不存在具有此类名称的模型", "28697ec15968a7969211f6d035ba9260": "{{polymorphic}} {0} 关系：{1} 不期望参数 `model`", "2c4904377a87fdab502118719cc0d266": "不支持 {{Transaction}}", "2c5c8519721f749aab13c2f04f41d611": "{0} 属性具有无效子句 {1}：预期精确为 2 个值，接收 {2}", "2f4af31c144bbfab1bbf479866acd820": "\n警告：未作为以下任何模块安装 {{LoopBack}} 连接器“{0}”\n\n {1}\n\n要修正，请运行：\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "警告：模型 {0} {{strict mode: `throw`}} 已移除，请使用 {{`strict: true`}} 代替，其将返回未知属性的 {{`Validation Error`}}。", "38dbf42c29a4645238cc3d632e88ebc9": "未针对关系 {0} 定义 {{Relation.modelTo}}，并且不是 {{polymorphic}}", "3cde8cc9bca22c67278b202ab0720106": "对于 {1}，找不到具有标识 {0} 的实例", "416dfbb7b823f51c9f3800be81060b41": "对于 {1}，找不到具有 {{id}} {0} 的实例", "49b5afd8c6a19ad9c8abeffb2f8114eb": "不推荐使用 BelongsTo 方法“getAsync()”，请改为使用“get()”。", "4c78325cedbb826db3a05bf5df0e8546": "在替换时必须提供 {{id}}！", "4e31b1edd10dadb724d83387de0b5062": "{{limit}} 参数 {0} 无效", "514985b2327f061ffb1c932f6b909979": "未定义模型 {0}。", "525c856e65daab43be247e7b5410febd": "{{polymorphic}} {0} 关系：定义定制 `foreignKey`/`discriminator` 时，{1} 不期望参数 `polymorphic.selector`", "5c18ee111dd87540cdb19a2a93b33be9": "事务因超时而回滚", "5ec7e6664256f7ea78f4f06dafc7d974": "事务未就绪，请等待返回的约定解决", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "无 {{id}} 名称 {0}", "614e3355647e4127c96256102dc63376": "{0} 属性具有无效子句 {1}：预期为字符串或 RegExp", "62a2d80c405b7fec5f547c448ab1b6ff": "{{order}} {0} 具有无效的方向", "6502a117987610380b9068ef98b1b0ee": "在 {0} 中找不到以下项的记录：({1}.{2} ,{3}.{4})", "67c2bf43b5281ab929617423ea8a6f3e": "连接器 {0} 不支持 {{replaceById}} 操作。这不是 LoopBack 中的错误。请联系连接器的作者，首选通过 GitHub 提交问题。", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}} 需要 {{id}} 自变量", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}} 需要 {{id}} 自变量", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "{{HasOne}} 关系无法创建 {0} 的多个实例", "728232e473bf80272c042df2b7e002f4": "{{polymorphic}} {0} 关系：提供参数 `polymorphic.foreignKey` 时，{1} 需要参数 `polymorphic.discriminator`", "791ab3031a73ede03f7d6299a85e8289": "在 {0} 毫秒后连接超时", "7b277018e43d41bc445731092b91547d": "未连接", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}} 需要 {{id}} 自变量", "7e9530c0399289be0ee601a604be71ff": "{{BelongsTo}} 关系 {0} 为空", "7faa840eb6ce11250a141deb42a6c489": "未知的关系 {{scope}}：{0}", "8091838319a5cc7d6a34af2f2a616ce9": "属性名称不应是模型中的“{{constructor}}”：{0}", "881e4b0cb86ed59549248ee540a9fd10": "{0} 数据中不允许属性名称“{{constructor}}”", "89afd3a9249f5a8d3edda07d84ca049d": "找不到 {{Polymorphic}} 模型：“{0}”", "89bf6d92731fe7bd2146ce8d0bec205c": "无效的自变量，必须是字符串、{{regex}} 字面值或 {{RegExp}} 对象", "8a39126103a157f501affa070367a1b0": "{0} 实例无效。详细信息：{1}。", "8c5ab01638c1ac1d58168c6346a8481a": "无效的 {{regex}} 标志：{0}", "938401ea4ce48159efa9be1d4a5e8bab": "项必须是数组：{0}", "9e1f143ee02946324d34da92f71bf74e": "{0} 关系：{1} 需要参数 `model`", "a004f310d315e592843776fab964eaeb": "{{Polymorphic}} 关系需要直通模型", "a0cf0e09c26df14283223e84e6a10f00": "无法更新属性。包含 {{id}} {0} 的 {{Object}} 不存在！", "a2487abefef4259c2131d96cdb8543b1": "连接失败：{0}\n将在下一次请求时重试。", "a25e41a39c60c4702e55d0c3936576a1": "键不匹配：{0}.{1}: {2}, {3}.{4}: {5}", "a327355560d495454fba2c1aad6bdf09": "未知的作用域方法：{0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "运行“{{npm install loopback-datasource-juggler}} {0}”命令", "a829dee089c912e68c18920ba015400c": "警告：对于以下模型，{{id}} 属性无法从 {0} 更改为 {1}：{{'loaded'}} 操作挂钩中的 {2}", "a984a076c59e451948b2bcf7a393d860": "警告：对于以下模型，{{id}} 属性无法从 {0} 更改为 {1}：{{'before save'}} 操作挂钩中的 {2}", "ac04cf275b71c1eb89a41cf6bbad7a64": "不推荐使用 HasOne 方法“getAsync()”，请改为使用“get()”。", "b138294f132edfe1eb2a8211150c7238": "查询中存在意外的“未定义”", "b15b20280211ad258d92947f05b6e4a5": "连接器尚未进行初始化。", "b278876ec93ef9760f00e83f38ba313d": "不推荐使用 Scope 方法“getAsync()”，请改为使用“find()”。", "ba0fd8106eb54de4d003a844206431fd": "不推荐使用模型挂钩“{0}”，请改用操作挂钩。{{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "Where 子句 {0} 不是 {{object}}", "bdb11cc1c780c9ccac33c316cfdc9d82": "未针对属性 {0}.{1} 定义类型", "bdfb951c8ff7ce0cbc08c06f548fd927": "值为空的 {{object}}", "bec226891a505828bfc76c5cfd73b336": "无法获取未知键 {0} 的 TTL", "cd930369e86cdd222f7bd117c6f9fa94": "未知的缺省值提供程序 {0}", "cfee4d8149316d9a647c0885cf3cafaf": "不支持包含点的属性名称。模型：{0}，动态属性：{1}", "d40328eabd8756d795bcdd49d782d4e9": "数据源不支持事务", "da02dd6c53d4148320eeb31718a7aebe": "属性 {0} 的类型无效", "da751a8a748adbde5b55fa83b707b4e2": "不支持包含点的属性名称。模型：{0}，属性：{1}", "db03083e9a768388fdbee865249ac67a": "正在忽略 {{updateOrCreate()}} 中的验证错误：", "dd63416d9b7d9fa4181e89efd619dfd8": "值不是具有有序数字索引的 {{array}} 或 {{object}}", "ddf0aa14803f1c84f4a97f3803f7471c": "类名是必需的", "e08ab0e1ab55f26c357061447b635905": "在 {0} 中找不到以下项的关系：({1}.{2} ,{3}.{4})", "e0e9504e137a3c3339144b51ed76fef2": "连接器定义不正确：应创建数据源的“{{connector}}”成员", "e2f282cbe3efba001d6d3a09f7f6ca8c": "{{polymorphic}} {0} 关系：提供参数 `polymorphic.discriminator` 时，{1} 需要参数 `polymorphic.foreignKey`", "e39e0f5d52bfbf511e645d19ecadd2fa": "{0} 属性有无效子句 {1}：{2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "未知的“{0}”{{id}}“{1}”。", "e54d944c2a2c85a23caa86027ae307cf": "无法迁移未附加到此数据源的模型：{0}", "e54f118a75e15e132f16b985274eb46d": "查询过滤器 {0} 不是 {{object}}", "e55937649d8d7a11706b8cec22d02eae": "{{HasOne}} 关系 {0} 为空", "e6161ae8459c79d810e2aa9d21282a39": "在更新属性时必须提供 {{id}}！", "eb56c2b0c30cf006e2df00a549ec9c2c": "未针对 {1} 模型定义关系“{0}”", "ec42dca074f1818c447f7ad16e2d01af": "连接的连接器未提供 {0}", "ecb7aa804bf54c682999d20d6436104c": "{{transaction}} 不活动：{0}", "f30809cb932b72a66416a709c8531530": "连接器不支持某个事务内的 {{method}}", "f41bd91dc0f000a79c0bf842f1b7fdf9": "无法从 JSON 字符串创建列表：{0}", "f6e8c96c93b9c7687d6c172b3695e898": "{{id}} 属性 ({0}) 无法从 {1} 更新为 {2}", "fa9ae17e8e008d0eb0f0421a2972308c": "{{polymorphic}} {0} 关系：{1} 需要参数 `model`", "fca4d12faff1035d9d0438d73432571b": "{0}.{1} 的重复条目", "fd3cc89dc67e2d604eaae21bdf41d403": "针对模型 {1} 找不到关系 {0}", "fec8ebda24db46a9d040bf863765cc44": "{0} 操作员有无效子句 {1}：{2}"}