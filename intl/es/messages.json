{"0483a77cf77741504204e5c066597487": "Relación {0} {{polymorphic}}: {1} no espera el parámetro `polymorphic.as` al definir `foreignKey`/`discriminator` personalizado ", "09483e03b91c8bd58732a74b3ef1ec13": "<PERSON><PERSON> no válida: {0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany ha recibido un destino que no contiene el \"{0}\" obligatorio", "0b16d3ffc42f91b4b9a4b3b50c41c838": "El orden {0} no es válido", "0bd753a8944ad0af85a939bb25273887": "La clave desconocida {0} no puede caducar", "0c0b867aca0973ba26e887d3337cc4ec": "Modelo {{Polymorphic}} no encontrado: `{0}` no establecido", "0c4eb8b6c2ff6e51d7e195eee346ced9": "La tabla '{0}' no existe.", "0ff31abb394afb555df162e74ff1a0a0": "{{id}} no puede actualizarse de {0} a {1} cuando {{forceId}} está establecido en true", "1ae7d3e0be381efb32bfd1ba652f5172": "AVISO: Relación {0} {{polymorphic}}: {1} utiliza la c¡palabra clave `polymorphic.as` que estará EN DESUSO en LoopBack.next, reconsulte este documento para conocer las posibles sustituciones (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "Las transacciones anidadas no están soportadas", "21095484501dbff31af6556fa6039182": "El parámetro de {{offset/skip}} {0} no es válido", "280f4550f90e133118955ec6f6f72830": "Se ha especificado el tipo de discriminador {0}, pero no existe ningún modelo con ese nombre", "28697ec15968a7969211f6d035ba9260": "Relación {0} {{polymorphic}}: {1} no espera el parámetro `model`", "2c4904377a87fdab502118719cc0d266": "{{Transaction}} no está soportada.", "2c5c8519721f749aab13c2f04f41d611": "La propiedad {0} tiene una cláusula {1} no válida: se esperaban exactamente 2 valores, se han recibido {2}", "2f4af31c144bbfab1bbf479866acd820": "\nAVISO: el conector {{LoopBack}} \"{0}\" no está instalado como ninguno de los módulos siguientes:\n\n {1}\n\nPara solucionarlo, ejecute:\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "Aviso: el modelo {0}, {{strict mode: `throw`}} se ha eliminado; utilice {{`strict: true`}} en su lugar, que devuelve {{`Validation Error`}} para propiedades desconocidas.", "38dbf42c29a4645238cc3d632e88ebc9": "{{Relation.modelTo}} no se ha definido para la relación {0} y no es {{polymorphic}}", "3cde8cc9bca22c67278b202ab0720106": "No se ha encontrado ninguna instancia con el ID {0} para {1}", "416dfbb7b823f51c9f3800be81060b41": "No se ha encontrado ninguna instancia con el {{id}} {0} para {1}", "49b5afd8c6a19ad9c8abeffb2f8114eb": "El método BelongsTo \"getAsync()\" está en desuso, utilice \"get()\" en su lugar.", "4c78325cedbb826db3a05bf5df0e8546": "Debe proporcionar un {{id}} al sustituir.", "4e31b1edd10dadb724d83387de0b5062": "El parámetro de {{limit}} {0} no es válido", "514985b2327f061ffb1c932f6b909979": "El modelo {0} no se ha definido.", "525c856e65daab43be247e7b5410febd": "Relación {0} {{polymorphic}}: {1} no espera el parámetro `polymorphic.selector` al definir `foreignKey`/`discriminator` personalizado ", "5c18ee111dd87540cdb19a2a93b33be9": "La transacción se ha retrotraído debido a tiempo de espera excedido", "5ec7e6664256f7ea78f4f06dafc7d974": "La transacción no está lista, espere la promesa devuelta para resolver", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "Ningún {{id}} de nombre {0}", "614e3355647e4127c96256102dc63376": "La propiedad {0} tiene una cláusula {1} no válida: se esperaba una serie o expresión regular", "62a2d80c405b7fec5f547c448ab1b6ff": "El {{order}} {0} tiene una dirección no válida", "6502a117987610380b9068ef98b1b0ee": "No se ha encontrado ningún registro en {0} para ({1}.{2} ,{3}.{4})", "67c2bf43b5281ab929617423ea8a6f3e": "El conector {0} no admite la operación {{replaceById}}. Esto no es un error en LoopBack. Póngase en contacto con los autores del conector, preferiblemente a través de cuestiones GitHub.", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}} requiere el argumento {{id}}", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}} requiere el argumento {{id}}", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "La relación {{HasOne}} no puede crear más de una instancia de {0}", "728232e473bf80272c042df2b7e002f4": "Relación {0} {{polymorphic}}: {1} requiere el parámetro `polymorphic.discriminator` cuando se proporciona el parámetro `polymorphic.foreignKey`", "791ab3031a73ede03f7d6299a85e8289": "Tiempo de espera agotado al conectarse después de {0} ms", "7b277018e43d41bc445731092b91547d": "No conectado", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}} requiere el argumento {{id}}", "7e9530c0399289be0ee601a604be71ff": "La relación {{BelongsTo}} {0} está vacía", "7faa840eb6ce11250a141deb42a6c489": "Relación desconocida {{scope}}: {0}", "8091838319a5cc7d6a34af2f2a616ce9": "El nombre de propiedad no debe ser \"{{constructor}}\" en el modelo: {0}", "881e4b0cb86ed59549248ee540a9fd10": "El nombre de propiedad \"{{constructor}}\" no está permitido en datos de {0}", "89afd3a9249f5a8d3edda07d84ca049d": "Modelo {{Polymorphic}} no encontrado: `{0}`", "89bf6d92731fe7bd2146ce8d0bec205c": "Argumento no válido, debe ser una serie, literal de {{regex}} u objeto {{RegExp}}", "8a39126103a157f501affa070367a1b0": "La instancia {0} no es válida. Detalles: {1}.", "8c5ab01638c1ac1d58168c6346a8481a": "Distintivos de {{regex}} no válidos: {0}", "938401ea4ce48159efa9be1d4a5e8bab": "Items debe ser una matriz: {0}", "9e1f143ee02946324d34da92f71bf74e": "Relación {0}: {1} requiere el parámetro `model`", "a004f310d315e592843776fab964eaeb": "Las relaciones {{Polymorphic}} necesitan un modelo definido", "a0cf0e09c26df14283223e84e6a10f00": "No se han podido actualizar atributos. {{Object}} con {{id}} {0} no existe.", "a2487abefef4259c2131d96cdb8543b1": "La conexión falla: {0}\nSe reintentará en la siguiente solicitud.", "a25e41a39c60c4702e55d0c3936576a1": "Discrepancia de claves: {0}.{1}: {2}, {3}.{4}: {5}", "a327355560d495454fba2c1aad6bdf09": "Método de ámbito desconocido: {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "Ejecute el mandato \"{{npm install loopback-datasource-juggler}} {0}\" ", "a829dee089c912e68c18920ba015400c": "AVISO: la propiedad {{id}} no puede cambiarse de {0} a {1} para el modelo:{2} en el gancho de operación {{'loaded'}}", "a984a076c59e451948b2bcf7a393d860": "AVISO: la propiedad {{id}} no puede cambiarse de {0} a {1} para el modelo:{2} en el gancho de operación {{'before save'}}", "ac04cf275b71c1eb89a41cf6bbad7a64": "<PERSON> método Has<PERSON>ne \"getAsync()\" está en desuso, utilice \"get()\" en su lugar.", "b138294f132edfe1eb2a8211150c7238": "`undefined` inesperado en la consulta", "b15b20280211ad258d92947f05b6e4a5": "El conector no se ha inicializado.", "b278876ec93ef9760f00e83f38ba313d": "<PERSON> método <PERSON> \"getAsync()\" está en desuso, utilice \"find()\" en su lugar.", "ba0fd8106eb54de4d003a844206431fd": "El gancho de modelo \"{0}\" está en desuso, utilice ganchos de operación en su lugar. {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "La cláusula where {0} no es un {{object}}", "bdb11cc1c780c9ccac33c316cfdc9d82": "Tipo no definido para la propiedad {0}.{1}", "bdfb951c8ff7ce0cbc08c06f548fd927": "El valor es un {{object}} vacío", "bec226891a505828bfc76c5cfd73b336": "No se puede obtener TTL para la clave desconocida {0}", "cd930369e86cdd222f7bd117c6f9fa94": "Proveedor de valor predeterminado desconocido {0}", "cfee4d8149316d9a647c0885cf3cafaf": "Los nombres de propiedad que contienen puntos no están soportados. Modelo: {0}, propiedad dinámica: {1}", "d40328eabd8756d795bcdd49d782d4e9": "DataSource no da soporte a las transacciones", "da02dd6c53d4148320eeb31718a7aebe": "Tipo no válido para la propiedad {0}", "da751a8a748adbde5b55fa83b707b4e2": "Los nombres de propiedad que contienen puntos no están soportados. Modelo: {0}, propiedad: {1}", "db03083e9a768388fdbee865249ac67a": "Se ignoran los errores de validación en {{updateOrCreate()}}:", "dd63416d9b7d9fa4181e89efd619dfd8": "El valor no es una {{array}} u {{object}} con índices numéricos secuenciales", "ddf0aa14803f1c84f4a97f3803f7471c": "Nombre de clase obligatorio", "e08ab0e1ab55f26c357061447b635905": "No se ha encontrado ninguna relación en {0} para ({1}.{2},{3}.{4})", "e0e9504e137a3c3339144b51ed76fef2": "El conector no está definido correctamente: debe crear el miembro `{{connector}}` de dataSource", "e2f282cbe3efba001d6d3a09f7f6ca8c": "Relación {0} {{polymorphic}}: {1} requiere el parámetro `polymorphic.foreignKey` cuando se proporciona el parámetro `polymorphic.discriminator`", "e39e0f5d52bfbf511e645d19ecadd2fa": "La propiedad {0} tiene una cláusula {1} no válida: {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "{{id}} de \"{0}\" desconocido \"{1}\".", "e54d944c2a2c85a23caa86027ae307cf": "No se pueden migrar modelos no conectados a este origen de datos: {0}", "e54f118a75e15e132f16b985274eb46d": "El filtro de consultas {0} no es un {{object}}", "e55937649d8d7a11706b8cec22d02eae": "la relación {{Has<PERSON><PERSON>}} {0} está vacía", "e6161ae8459c79d810e2aa9d21282a39": "Debe proporcionar un {{id}} al actualizar atributos.", "eb56c2b0c30cf006e2df00a549ec9c2c": "La relación \"{0}\" no está definida para el modelo {1}", "ec42dca074f1818c447f7ad16e2d01af": "El conector asociado no ha proporcionado {0}", "ecb7aa804bf54c682999d20d6436104c": "La {{transaction}} no está activa: {0}", "f30809cb932b72a66416a709c8531530": "El conector no admite {{method}} dentro de una transacción", "f41bd91dc0f000a79c0bf842f1b7fdf9": "no se ha podido crear la lista a partir de la serie JSON: {0}", "f6e8c96c93b9c7687d6c172b3695e898": "La propiedad de {{id}} ({0}) no puede actualizarse de {1} a {2}", "fa9ae17e8e008d0eb0f0421a2972308c": "Relación {0}:{{polymorphic}}: {1} requiere el parámetro `model`", "fca4d12faff1035d9d0438d73432571b": "Entrada duplicada para {0}.{1}", "fd3cc89dc67e2d604eaae21bdf41d403": "No se ha podido encontrar la relación {0} para el modelo {1}", "fec8ebda24db46a9d040bf863765cc44": "El operador {0} tiene cl<PERSON>ulas no válidas {1}: {2}"}