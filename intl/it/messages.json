{"0483a77cf77741504204e5c066597487": "{{polymorphic}} Relazione {0}: {1} non prevedere il parametro `polymorphic.as` durante la definizione di `foreignKey`/`discriminator` personalizzato ", "09483e03b91c8bd58732a74b3ef1ec13": "Data non valida: {0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany ha ricevuto una destinazione che non contiene il valore \"{0}\" richiesto", "0b16d3ffc42f91b4b9a4b3b50c41c838": "L'ordine {0} non è valido", "0bd753a8944ad0af85a939bb25273887": "Impossibile fare scadere la chiave sconosciuta {0}", "0c0b867aca0973ba26e887d3337cc4ec": "<PERSON>lo {{Polymorphic}} non trovato: `{0}` non impostato", "0c4eb8b6c2ff6e51d7e195eee346ced9": "La tabella '{0}' non esiste.", "0ff31abb394afb555df162e74ff1a0a0": "Impossibile aggiornare {{id}} da {0} a {1} quando {{forceId}} è impostato su true", "1ae7d3e0be381efb32bfd1ba652f5172": "AVVERTENZA: {{polymorphic}} La relazione {0}: {1} utilizza la parola chiave `polymorphic.as` che sarà OBSOLETA in LoopBack.next, fare riferimento a questa documentazione per le soluzioni di sostituzione (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "La nidificazione delle transazioni non è supportata", "21095484501dbff31af6556fa6039182": "Il parametro {{offset/skip}} {0} non è valido", "280f4550f90e133118955ec6f6f72830": "È stato specificato il tipo di discriminatore {0} ma non esiste alcun modello con tale nome", "28697ec15968a7969211f6d035ba9260": "{{polymorphic}} La relazione {0}: {1} non prevede il parametro `model`", "2c4904377a87fdab502118719cc0d266": "{{Transaction}} non supportata", "2c5c8519721f749aab13c2f04f41d611": "La proprietà {0} ha la clausola non valida {1}: Previsti esattamente 2 valori, rice<PERSON><PERSON> {2}", "2f4af31c144bbfab1bbf479866acd820": "\nAVVERTENZA: il connettore {{LoopBack}} \"{0}\" non è installato come nessuno dei seguenti moduli:\n\n {1}\n\nPer correggere, eseguire:\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "Avvertenza: il modello {0}, {{strict mode: `throw`}} è stato rimosso, utilizzare {{`strict: true`}}, che restituisce {{`Validation Error`}} per le proprietà sconosciute,", "38dbf42c29a4645238cc3d632e88ebc9": "{{Relation.modelTo}} non è definito per la relazione {0} e non è {{polymorphic}}", "3cde8cc9bca22c67278b202ab0720106": "Nessuna istanza con ID {0} trovata per {1}", "416dfbb7b823f51c9f3800be81060b41": "Nessuna istanza con {{id}} {0} trovata per {1}", "49b5afd8c6a19ad9c8abeffb2f8114eb": "Il metodo BelongsTo \"getAsync()\" è obsoleto, utilizzare \"get()\".", "4c78325cedbb826db3a05bf5df0e8546": "È necessario fornire un {{id}} durante la sostituzione.", "4e31b1edd10dadb724d83387de0b5062": "Il parametro {{limit}} {0} non è valido", "514985b2327f061ffb1c932f6b909979": "Il modello {0} non è definito.", "525c856e65daab43be247e7b5410febd": "{{polymorphic}} La relazione {0}: {1} non prevede il parametro `polymorphic.selector` durante la definizione di `foreignKey`/`discriminator` personalizzato ", "5c18ee111dd87540cdb19a2a93b33be9": "Viene effettuato il rollback della transazione a causa del timeout", "5ec7e6664256f7ea78f4f06dafc7d974": "La transazione non è pronta, attendere la promessa di risolvere restituita", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "Nessun nome {{id}} {0}", "614e3355647e4127c96256102dc63376": "La proprietà {0} ha una clausola non valida {1}: Prevista una stringa o RegExp", "62a2d80c405b7fec5f547c448ab1b6ff": "{{order}} {0} ha una direzione non valida", "6502a117987610380b9068ef98b1b0ee": "Nessun record trovato in {0} per ({1}.{2} ,{3}.{4})", "67c2bf43b5281ab929617423ea8a6f3e": "Il connettore {0} non supporta l'operazione {{replaceById}}. Questo non è un bug in LoopBack. Contattare gli autori del connettore, preferibilmente mediante GitHub.", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}} richiede l'argomento {{id}}", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}} richiede l'argomento {{id}}", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "La relazione {{Has<PERSON>ne}} non può creare più di una istanza di {0}", "728232e473bf80272c042df2b7e002f4": "{{polymorphic}} La relazione {0}: {1} richiede il parametro `polymorphic.discriminator` quando viene fornito il parametro `polymorphic.foreignKey`", "791ab3031a73ede03f7d6299a85e8289": "Timeout nella connessione dopo {0} ms", "7b277018e43d41bc445731092b91547d": "Non connesso", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}} richiede l'argomento {{id}}", "7e9530c0399289be0ee601a604be71ff": "La relazione {{BelongsTo}} {0} è vuota", "7faa840eb6ce11250a141deb42a6c489": "Relazione sconosciuta {{scope}}: {0}", "8091838319a5cc7d6a34af2f2a616ce9": "Il nome della proprietà non deve essere \"{{constructor}}\" nel modello: {0}", "881e4b0cb86ed59549248ee540a9fd10": "Nome della proprietà \"{{constructor}}\" non consentito nei dati {0}", "89afd3a9249f5a8d3edda07d84ca049d": "<PERSON><PERSON> {{Polymorphic}} non trovato: `{0}`", "89bf6d92731fe7bd2146ce8d0bec205c": "Argomento non valido, deve essere una stringa, un valore letterale {{regex}} o un oggetto {{RegExp}}", "8a39126103a157f501affa070367a1b0": "L'istanza {0} non è valida. Dettagli: {1}.", "8c5ab01638c1ac1d58168c6346a8481a": "Indicatori {{regex}} non validi: {0}", "938401ea4ce48159efa9be1d4a5e8bab": "Gli elementi devono essere un array: {0}", "9e1f143ee02946324d34da92f71bf74e": "La relazione {0}: {1} richiede il parametro `model`", "a004f310d315e592843776fab964eaeb": "Le relazioni {{Polymorphic}} richiedono un modello di passaggio", "a0cf0e09c26df14283223e84e6a10f00": "Impossibile aggiornare gli attributi. {{Object}} con {{id}} {0} non esiste.", "a2487abefef4259c2131d96cdb8543b1": "Errore della connessione: {0}\nVerrà eseguito un nuovo tentativo per la richiesta successiva.", "a25e41a39c60c4702e55d0c3936576a1": "Mancata corrispondenza della chiave: {0}.{1}: {2}, {3}.{4}: {5}", "a327355560d495454fba2c1aad6bdf09": "Metodo dell'ambito sconosciuto: {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "Eseguire il comando \"{{npm install loopback-datasource-juggler}} {0}\" ", "a829dee089c912e68c18920ba015400c": "AVVERTENZA: impossibile modificare la proprietà {{id}} da {0} a {1} per il modello:{2} nell'hook operazione {{'loaded'}}", "a984a076c59e451948b2bcf7a393d860": "AVVERTENZA: impossibile modificare la proprietà {{id}} da {0} a {1} per il modello:{2} nell'hook operazione {{'before save'}}", "ac04cf275b71c1eb89a41cf6bbad7a64": "<PERSON> metodo <PERSON> \"getAsync()\" è obsoleto, utilizzare \"get()\".", "b138294f132edfe1eb2a8211150c7238": "Elemento `undefined` non previsto nella query", "b15b20280211ad258d92947f05b6e4a5": "Il connettore non è stato inizializzato.", "b278876ec93ef9760f00e83f38ba313d": "Il metodo dell'ambito \"getAsync()\" è obsoleto, utilizzare \"find()\" invece.", "ba0fd8106eb54de4d003a844206431fd": "L'hook del modello \"{0}\" è obsoleto, utilizzare gli hook dell'operazione. {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "La clausola where {0} non è un {{object}}", "bdb11cc1c780c9ccac33c316cfdc9d82": "Tipo non definito per la proprietà {0}.{1}", "bdfb951c8ff7ce0cbc08c06f548fd927": "Il valore è un {{object}} vuoto", "bec226891a505828bfc76c5cfd73b336": "Impossibile acquisire TTL per la chiave sconosciuta {0}", "cd930369e86cdd222f7bd117c6f9fa94": "Provider del valore predefinito sconosciuto {0}", "cfee4d8149316d9a647c0885cf3cafaf": "I nomi delle proprietà che contengono punti non sono supportati. Modello: {0}, proprietà dinamica: {1}", "d40328eabd8756d795bcdd49d782d4e9": "DataSource non supporta le transazioni", "da02dd6c53d4148320eeb31718a7aebe": "Tipo non valido per la proprietà {0}", "da751a8a748adbde5b55fa83b707b4e2": "I nomi delle proprietà che contengono punti non sono supportati. Modello: {0}, proprietà: {1}", "db03083e9a768388fdbee865249ac67a": "Errore di convalida in {{updateOrCreate()}} ignorati:", "dd63416d9b7d9fa4181e89efd619dfd8": "Il valore non è un {{array}} o {{object}} con indici numerici sequenziali", "ddf0aa14803f1c84f4a97f3803f7471c": "Nome della classe obbligatorio", "e08ab0e1ab55f26c357061447b635905": "Nessuna relazione trovata in {0} per ({1}.{2},{3}.{4})", "e0e9504e137a3c3339144b51ed76fef2": "Il connettore non è definito correttamente: deve creare il membro `{{connector}}` di dataSource", "e2f282cbe3efba001d6d3a09f7f6ca8c": "{{polymorphic}} La relazione {0}: {1} richiede il parametro `polymorphic.foreignKey` quando viene fornito il parametro `polymorphic.discriminator`", "e39e0f5d52bfbf511e645d19ecadd2fa": "La proprietà {0} ha una clausola non valida {1}: {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "{{id}} \"{0}\" sconosciuto \"{1}\".", "e54d944c2a2c85a23caa86027ae307cf": "Impossibile migrare i modelli non allegati a questa origine dati: {0}", "e54f118a75e15e132f16b985274eb46d": "Il filtro della query {0} non è un {{object}}", "e55937649d8d7a11706b8cec22d02eae": "La relazione {{<PERSON><PERSON><PERSON>}} {0} è vuota", "e6161ae8459c79d810e2aa9d21282a39": "È necessario fornire un {{id}} durante l'aggiornamento degli attributi.", "eb56c2b0c30cf006e2df00a549ec9c2c": "Relazione \"{0}\" non definita per il modello {1}", "ec42dca074f1818c447f7ad16e2d01af": "{0} non fornito dal connettore collegato", "ecb7aa804bf54c682999d20d6436104c": "La {{transaction}} non è attiva: {0}", "f30809cb932b72a66416a709c8531530": "Il connettore non supporta {{method}} all'interno di una transazione", "f41bd91dc0f000a79c0bf842f1b7fdf9": "impossibile creare un elenco dalla stringa JSON: {0}", "f6e8c96c93b9c7687d6c172b3695e898": "Impossibile aggiornare la proprietà {{id}} ({0}) da {1} a {2}", "fa9ae17e8e008d0eb0f0421a2972308c": "{{polymorphic}} La relazione {0}: {1} richiede il parametro `model`", "fca4d12faff1035d9d0438d73432571b": "Voce duplicata per {0}.{1}", "fd3cc89dc67e2d604eaae21bdf41d403": "Impossibile trovare la relazione {0} per il modello {1}", "fec8ebda24db46a9d040bf863765cc44": "L'operatore {0} ha delle clausole non valide {1}: {2}"}