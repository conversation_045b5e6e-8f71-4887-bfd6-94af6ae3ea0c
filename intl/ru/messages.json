{"0483a77cf77741504204e5c066597487": "Связь {0} {{polymorphic}}: {1} не предполагает параметр `polymorphic.as` при определении пользовательского атрибута `foreignKey`/`discriminator` ", "09483e03b91c8bd58732a74b3ef1ec13": "Недопустимая дата: {0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany полученный целевой объект не содержит обязательный параметр \"{0}\"", "0b16d3ffc42f91b4b9a4b3b50c41c838": "Недопустимый порядок {0}", "0bd753a8944ad0af85a939bb25273887": "Не удалось преобразовать неизвестный ключ {0} в устаревший", "0c0b867aca0973ba26e887d3337cc4ec": "Модель {{Polymorphic}} не найдена: не задан параметр `{0}`", "0c4eb8b6c2ff6e51d7e195eee346ced9": "Таблица '{0}' не существует.", "0ff31abb394afb555df162e74ff1a0a0": "Для {{id}} не удается изменить значение {0} на {1}, если параметру {{forceId}} присвоено значение true", "1ae7d3e0be381efb32bfd1ba652f5172": "ПРЕДУПРЕЖДЕНИЕ: связь {0} {{polymorphic}}: {1} использует ключевое слово `polymorphic.as`, которое УСТАРЕЕТ в LoopBack.next. Решение по замене приведены в следующем документе: (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "Вложенные транзакции не поддерживаются", "21095484501dbff31af6556fa6039182": "Параметр {{offset/skip}} {0} недопустим", "280f4550f90e133118955ec6f6f72830": "Указан тип дискриминатора {0}, но модель с таким именем не существует", "28697ec15968a7969211f6d035ba9260": "Связь {0} {{polymorphic}}: в {1} не используется параметр `model`", "2c4904377a87fdab502118719cc0d266": "{{Transaction}} не поддерживается", "2c5c8519721f749aab13c2f04f41d611": "Свойство {0} содержит недопустимый оператор {1}: требуется ровно 2 значения, но получено {2}", "2f4af31c144bbfab1bbf479866acd820": "\nПРЕДУПРЕЖДЕНИЕ: Коннектор {{LoopBack}} \"{0}\" не установлен как один из следующих модулей:\n\n {1}\n\nДля исправления этой ошибки выполните следующую команду:\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "Предупреждение: Модель {0} {{strict mode: `throw`}} была удалена, используйте вместо нее {{`strict: true`}}, возвращающий для неизвестных свойств значение {{`Validation Error`}},", "38dbf42c29a4645238cc3d632e88ebc9": "Параметр {{Relation.modelTo}} не определен для связи {0} и не является {{polymorphic}}", "3cde8cc9bca22c67278b202ab0720106": "Не найден экземпляр с ИД {0} для {1}", "416dfbb7b823f51c9f3800be81060b41": "Не найден экземпляр с {{id}} {0} для {1}", "49b5afd8c6a19ad9c8abeffb2f8114eb": "<PERSON><PERSON>т<PERSON><PERSON> BelongsTo \"getAsync()\" устарел, используйте вместо него \"get()\".", "4c78325cedbb826db3a05bf5df0e8546": "При замене необходимо указать {{id}}!", "4e31b1edd10dadb724d83387de0b5062": "Параметр {{limit}} {0} недопустим", "514985b2327f061ffb1c932f6b909979": "Модель {0} не определена.", "525c856e65daab43be247e7b5410febd": "Связь {0} {{polymorphic}}: {1} не предполагает параметр `polymorphic.selector` при определении пользовательского атрибута `foreignKey`/`discriminator` ", "5c18ee111dd87540cdb19a2a93b33be9": "Выполнен откат транзакции из-за тайм-аута", "5ec7e6664256f7ea78f4f06dafc7d974": "Транзакция не готова, дождитесь возвращенного обязательства устранить неполадку", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "Отсутствует имя {{id}} {0}", "614e3355647e4127c96256102dc63376": "Свойство {0} содержит недопустимый оператор {1}: ожидается строка или регулярное выражение", "62a2d80c405b7fec5f547c448ab1b6ff": "Недопустимое направление {{order}} {0}", "6502a117987610380b9068ef98b1b0ee": "В {0} не обнаружены записи для ({1}.{2} ,{3}.{4})", "67c2bf43b5281ab929617423ea8a6f3e": "Коннектор {0} не поддерживает операцию {{replaceById}}. Это не является ошибкой LoopBack. Обратитесь к авторам коннектора, желательно через раздел ошибок на GitHub.", "6c3234937d69763fc7f6bcafccc59bbc": "Для {{Model::deleteById}} требуется аргумент {{id}}", "6eb6fd4fbd73394000bc25f5776fd20c": "Для {{Model::exists}} требуется аргумент {{id}}", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "Связи {{HasOne}} не удается создать больше одного экземпляра {0}", "728232e473bf80272c042df2b7e002f4": "Связь {0} {{polymorphic}}: для {1} требуется параметр `polymorphic.discriminator`, если задан параметр `polymorphic.foreignKey`", "791ab3031a73ede03f7d6299a85e8289": "Тайм-аут соединения наступает через {0} мс", "7b277018e43d41bc445731092b91547d": "Не подключено", "7bbbdece4eea90e42aa5c0bce295e503": "Для {{Model::findById}} требуется аргумент {{id}}", "7e9530c0399289be0ee601a604be71ff": "Пустая связь {{BelongsTo}} {0}", "7faa840eb6ce11250a141deb42a6c489": "Неизвестная связь {{scope}}: {0}", "8091838319a5cc7d6a34af2f2a616ce9": "Имя свойства не должно быть \"{{constructor}}\" в модели: {0}", "881e4b0cb86ed59549248ee540a9fd10": "Имя свойства \"{{constructor}}\" не разрешено в данных {0}", "89afd3a9249f5a8d3edda07d84ca049d": "Модель {{Polymor<PERSON>}} не найдена: `{0}`", "89bf6d92731fe7bd2146ce8d0bec205c": "Недопустимый аргумент, требуется строка, литерал {{regex}} или объект {{RegExp}}", "8a39126103a157f501affa070367a1b0": "Недопустимый экземпляр {0}. Сведения: {1}.", "8c5ab01638c1ac1d58168c6346a8481a": "Недопустимые флаги {{regex}}: {0}", "938401ea4ce48159efa9be1d4a5e8bab": "Элементы должны быть массивом: {0}", "9e1f143ee02946324d34da92f71bf74e": "Связь {0}: для {1} требуется параметр `model`", "a004f310d315e592843776fab964eaeb": "Связи {{Polymorphic}} требуется промежуточная модель", "a0cf0e09c26df14283223e84e6a10f00": "Не удалось обновить атрибуты. {{Object}} с {{id}} {0} не существует", "a2487abefef4259c2131d96cdb8543b1": "Соединение не выполнено: {0}\nПопытка соединения будет выполнена повторно при следующем запросе.", "a25e41a39c60c4702e55d0c3936576a1": "Несоответствие ключей: {0}.{1}: {2}, {3}.{4}: {5}", "a327355560d495454fba2c1aad6bdf09": "Неизвестный метод области: {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "Выполните команду \"{{npm install loopback-datasource-juggler}} {0}\" ", "a829dee089c912e68c18920ba015400c": "Предупреждение: значение свойства {{id}} {0} не может быть изменено на {1} для модели {2} в перехватчике операции {{'loaded'}}", "a984a076c59e451948b2bcf7a393d860": "Предупреждение: значение свойства {{id}} {0} не может быть изменено на {1} для модели {2} в перехватчике операции {{'before save'}}", "ac04cf275b71c1eb89a41cf6bbad7a64": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"getAsync()\" устарел, используйте вместо него \"get()\".", "b138294f132edfe1eb2a8211150c7238": "Непредвиденный параметр `undefined` в запросе", "b15b20280211ad258d92947f05b6e4a5": "Коннектор не инициализирован.", "b278876ec93ef9760f00e83f38ba313d": "<PERSON><PERSON><PERSON><PERSON><PERSON>ope \"getAsync()\" устарел, используйте вместо него \"find()\".", "ba0fd8106eb54de4d003a844206431fd": "Перехватчик Model \"{0}\" устарел, используйте вместо него перехватчики Operation. {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "Оператор where {0} не является объектом {{object}}", "bdb11cc1c780c9ccac33c316cfdc9d82": "Не определен тип для свойства {0}.{1}", "bdfb951c8ff7ce0cbc08c06f548fd927": "Значение - пустой объект {{object}}", "bec226891a505828bfc76c5cfd73b336": "Не удалось получить TTL для неизвестного ключа {0}", "cd930369e86cdd222f7bd117c6f9fa94": "Неизвестный поставщик значений по умолчанию {0}", "cfee4d8149316d9a647c0885cf3cafaf": "Не поддерживаются имена свойств, содержащие точку. Модель: {0}, динамическое свойство: {1}", "d40328eabd8756d795bcdd49d782d4e9": "DataSource не поддерживает транзакции", "da02dd6c53d4148320eeb31718a7aebe": "Недопустимый тип для свойства {0}", "da751a8a748adbde5b55fa83b707b4e2": "Не поддерживаются имена свойств, содержащие точку. Модель: {0}, свойство: {1}", "db03083e9a768388fdbee865249ac67a": "Ошибки проверки в {{updateOrCreate()}} игнорируются:", "dd63416d9b7d9fa4181e89efd619dfd8": "Значение не является {{array}} или {{object}} с последовательными числовыми индексами", "ddf0aa14803f1c84f4a97f3803f7471c": "Необходимо указать имя класса", "e08ab0e1ab55f26c357061447b635905": "В {0} не найдена связь для ({1}.{2},{3}.{4})", "e0e9504e137a3c3339144b51ed76fef2": "Неправильно определен коннектор: он должен создавать элемент `{{connector}}` в dataSource", "e2f282cbe3efba001d6d3a09f7f6ca8c": "Связь {0} {{polymorphic}}: для {1} требуется параметр `polymorphic.foreignKey`, если указан параметр `polymorphic.discriminator`", "e39e0f5d52bfbf511e645d19ecadd2fa": "Свойство {0} содержит недопустимый оператор {1}: {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "Неизвестный {{id}} \"{0}\" \"{1}\".", "e54d944c2a2c85a23caa86027ae307cf": "Не удается перенести модели, которые не подключены к этому источнику данных: {0}", "e54f118a75e15e132f16b985274eb46d": "Фильтр запроса {0} не является объектом {{object}}", "e55937649d8d7a11706b8cec22d02eae": "Пустая связь {{HasOne}} {0}", "e6161ae8459c79d810e2aa9d21282a39": "При обновлении атрибутов необходимо указать {{id}}", "eb56c2b0c30cf006e2df00a549ec9c2c": "Связь \"{0}\" не определена для модели {1}", "ec42dca074f1818c447f7ad16e2d01af": "Подключенный коннектор не указал {0}", "ecb7aa804bf54c682999d20d6436104c": "Неактивная транзакция {{transaction}}: {0}", "f30809cb932b72a66416a709c8531530": "Коннектор не поддерживает {{method}} в транзакции", "f41bd91dc0f000a79c0bf842f1b7fdf9": "Не удалось создать атрибут List из строки JSON: {0}", "f6e8c96c93b9c7687d6c172b3695e898": "Для свойства {{id}} ({0}) не удалось изменить значение {1} на {2}", "fa9ae17e8e008d0eb0f0421a2972308c": "Связь {0} {{polymorphic}}: для {1} требуется параметр `model`", "fca4d12faff1035d9d0438d73432571b": "Создать копию для {0}.{1}", "fd3cc89dc67e2d604eaae21bdf41d403": "Не удалось найти связь {0} для модели {1}", "fec8ebda24db46a9d040bf863765cc44": "Оператор {0} содержит недопустимые операторы {1}: {2}"}