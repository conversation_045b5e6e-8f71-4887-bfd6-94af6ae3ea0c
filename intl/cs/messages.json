{"0483a77cf77741504204e5c066597487": "Vztah {{polymorphic}} {0}: {1} neočekává parametr `polymorphic.as` p<PERSON><PERSON> def<PERSON> vlastn<PERSON>ho parametru `foreignKey`/`discriminator` ", "09483e03b91c8bd58732a74b3ef1ec13": "Neplatné datum: {0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany p<PERSON>, k<PERSON><PERSON> povinn<PERSON> \"{0}\"", "0b16d3ffc42f91b4b9a4b3b50c41c838": "Pořadí {0} nen<PERSON> platné", "0bd753a8944ad0af85a939bb25273887": "Nelze ukončit platnost neznámého klíče {0}", "0c0b867aca0973ba26e887d3337cc4ec": "Model {{Polymorphic}} nebyl nalezen: `{0}` ne<PERSON>aven", "0c4eb8b6c2ff6e51d7e195eee346ced9": "Tabulka '{0}' neexistuje.", "0ff31abb394afb555df162e74ff1a0a0": "{{id}} nelze aktualizovat z {0} na {1}, k<PERSON><PERSON> je {{forceId}} nastaveno na true", "1ae7d3e0be381efb32bfd1ba652f5172": "VAROVÁNÍ: Vztah {{polymorphic}} {0}: {1} používá klíčové slovo `polymorphic.as`, k<PERSON><PERSON> bude ZAMÍTNUTO v LoopBack.next, vit tato dokumentace s řešeními <PERSON> (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "Vnoření transakcí není pod<PERSON>", "21095484501dbff31af6556fa6039182": "Parametr {{offset/skip}} {0} nen<PERSON> platn<PERSON>", "280f4550f90e133118955ec6f6f72830": "<PERSON><PERSON> typ diskriminátoru {0}, ale neexist<PERSON><PERSON> model s tímto názvem", "28697ec15968a7969211f6d035ba9260": "Vztah {{polymorphic}} {0}: {1} neočekává parametr `model`", "2c4904377a87fdab502118719cc0d266": "{{Transaction}} ne<PERSON><PERSON>", "2c5c8519721f749aab13c2f04f41d611": "Vlastnost {0} má neplatnou klauzuli {1}: Očekávaly se přesně 2 hodnoty, p<PERSON><PERSON><PERSON> {2}", "2f4af31c144bbfab1bbf479866acd820": "\nVAROVÁNÍ: Konektor {{LoopBack}} \"{0}\" nen<PERSON> nainstal<PERSON>n jako <PERSON> z následujících modulů:\n\n {1}\n\nChcete-li provést opravu, spusťte:\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "Varování: Model {0}, {{strict mode: `throw`}} by<PERSON> o<PERSON><PERSON>, místo toho použi<PERSON>te {{`strict: true`}}, což vrací {{`Validation Error`}} pro nez<PERSON><PERSON> v<PERSON>,", "38dbf42c29a4645238cc3d632e88ebc9": "{{Relation.modelTo}} nen<PERSON> definován pro vztah {0} a není {{polymorphic}}", "3cde8cc9bca22c67278b202ab0720106": "Žádná instance s ID {0} nebyla nalezena pro {1}", "416dfbb7b823f51c9f3800be81060b41": "Žádná instance s {{id}} {0} nebyla nalezena pro {1}", "49b5afd8c6a19ad9c8abeffb2f8114eb": "Metoda BelongsTo \"getAsync()\" je <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, m<PERSON>to toho p<PERSON>ži<PERSON> \"get()\".", "4c78325cedbb826db3a05bf5df0e8546": "<PERSON>s<PERSON><PERSON> zadat {{id}} p<PERSON>i <PERSON>!", "4e31b1edd10dadb724d83387de0b5062": "Parametr {{limit}} {0} nen<PERSON> p<PERSON>", "514985b2327f061ffb1c932f6b909979": "Model {0} ne<PERSON><PERSON>.", "525c856e65daab43be247e7b5410febd": "Vztah {{polymorphic}} {0}: {1} neočekává parametr `polymorphic.selector` p<PERSON><PERSON> def<PERSON> vlastn<PERSON>ho parametru `foreignKey`/`discriminator` ", "5c18ee111dd87540cdb19a2a93b33be9": "Transakce je odvolána v důsledku vypršení časového limitu", "5ec7e6664256f7ea78f4f06dafc7d974": "Transakce není př<PERSON>na, počkejte na vyřešení vráceného závazku", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "Ž<PERSON><PERSON><PERSON> název {{id}} {0}", "614e3355647e4127c96256102dc63376": "Vlastnost {0} má neplatnou klauzuli {1}: <PERSON><PERSON> <PERSON>etězec nebo RegExp", "62a2d80c405b7fec5f547c448ab1b6ff": "{{order}} {0} má neplatný směr", "6502a117987610380b9068ef98b1b0ee": "Nebyl nalezen žádný záznam v {0} pro ({1}. {2}, {3}. {4})", "67c2bf43b5281ab929617423ea8a6f3e": "Konektor {0} nepodporuje operaci {{replaceById}}. Nejde o chybu ve zpětné smyčce. Obraťte se na autory konektoru, pokud možno přes problémy GitHub.", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}} vyžaduje argument {{id}}", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}} vyžaduje argument {{id}}", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "Vztah {{Has<PERSON>ne}} nemůže vytvořit více než jednu instanci {0}", "728232e473bf80272c042df2b7e002f4": "Vztah {{polymorphic}} {0}: {1} vyžaduje parametr `polymorphic.discriminator`, je-li dod<PERSON> parametr `polymorphic.foreignKey`", "791ab3031a73ede03f7d6299a85e8289": "Časový limit připojení po {0} ms", "7b277018e43d41bc445731092b91547d": "Nepřipojeno", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}} vyžaduje argument {{id}}", "7e9530c0399289be0ee601a604be71ff": "Vztah {{BelongsTo}} {0} je <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "7faa840eb6ce11250a141deb42a6c489": "Neznámý vztah {{scope}}: {0}", "8091838319a5cc7d6a34af2f2a616ce9": "<PERSON><PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON> \"{{constructor}}\" v modelu: {0}", "881e4b0cb86ed59549248ee540a9fd10": "Název vlastnosti \"{{constructor}}\" není povolen v datech {0}", "89afd3a9249f5a8d3edda07d84ca049d": "Model {{Polymorphic}} nebyl nalezen: `{0}`", "89bf6d92731fe7bd2146ce8d0bec205c": "<PERSON><PERSON><PERSON><PERSON><PERSON> argument, mus<PERSON> b<PERSON>, {{regex}} nebo {{RegExp}}", "8a39126103a157f501affa070367a1b0": "Instance {0} nen<PERSON> p<PERSON>. Podrobnosti: {1}.", "8c5ab01638c1ac1d58168c6346a8481a": "Neplat<PERSON>é <PERSON> {{regex}}: {0}", "938401ea4ce48159efa9be1d4a5e8bab": "<PERSON><PERSON><PERSON><PERSON> m<PERSON><PERSON> b<PERSON>t pole: {0}", "9e1f143ee02946324d34da92f71bf74e": "Vztah {0}: {1} vyžaduje parametr `model`", "a004f310d315e592843776fab964eaeb": "Vztahy {{Polymorphic}} vyžadují model typu through", "a0cf0e09c26df14283223e84e6a10f00": "Nelze aktualizovat atributy. {{Object}} s {{id}} {0} neexistuje!", "a2487abefef4259c2131d96cdb8543b1": "Připojení se nezdařilo: {0} \nBude zopakováno pro další požadavek.", "a25e41a39c60c4702e55d0c3936576a1": "Neshoda klíče: {0}. {1}: {2}, {3}. {4}: {5}", "a327355560d495454fba2c1aad6bdf09": "Neznámá metoda rozsahu: {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "Spustit příkaz \"{{npm install loopback-datasource-juggler}} {0}\"  ", "a829dee089c912e68c18920ba015400c": "VAROVÁNÍ: Vlastnost {{id}} nelze změnit z {0} na {1} pro model: {2} v háčku operace {{'loaded'}}", "a984a076c59e451948b2bcf7a393d860": "VAROVÁNÍ: Vlastnost {{id}} nelze změnit z {0} na {1} pro model: {2} v háčku operace {{'before save'}}", "ac04cf275b71c1eb89a41cf6bbad7a64": "<PERSON><PERSON> \"getAsync()\" je <PERSON><PERSON><PERSON><PERSON><PERSON>, m<PERSON>to toho p<PERSON>ži<PERSON>te \"get()\".", "b138294f132edfe1eb2a8211150c7238": "Neočekávaný parametr `undefined` v dotazu", "b15b20280211ad258d92947f05b6e4a5": "Konektor nebyl inicializován.", "b278876ec93ef9760f00e83f38ba313d": "<PERSON><PERSON> \"getAsync()\" je <PERSON><PERSON><PERSON><PERSON>, m<PERSON>to toho použ<PERSON>te \"find()\".", "ba0fd8106eb54de4d003a844206431fd": "Háček modelu \"{0}\" je <PERSON><PERSON><PERSON><PERSON>, místo toho použijte háčky operace. {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "K<PERSON><PERSON>le where {0} není {{object}}", "bdb11cc1c780c9ccac33c316cfdc9d82": "<PERSON>ení definován typ vlastnosti {0}.{1}", "bdfb951c8ff7ce0cbc08c06f548fd927": "Hodnota je prázdný {{object}}", "bec226891a505828bfc76c5cfd73b336": "Nelze získat TTL pro neznámý klíč {0}", "cd930369e86cdd222f7bd117c6f9fa94": "Neznámý poskytovatel výchozí hodnoty {0}", "cfee4d8149316d9a647c0885cf3cafaf": "Názvy vlastnosti obsahující tečku(y) nejsou pod<PERSON>. Model: {0}, dynamická vlastnost: {1}", "d40328eabd8756d795bcdd49d782d4e9": "<PERSON><PERSON><PERSON><PERSON> dat nepodporuje transakce", "da02dd6c53d4148320eeb31718a7aebe": "Neplatný typ pro vlastnost {0}", "da751a8a748adbde5b55fa83b707b4e2": "Názvy vlastnosti obsahující tečku(y) nejsou pod<PERSON>. Model: {0}, vlastnost: {1}", "db03083e9a768388fdbee865249ac67a": "Ignorují se chyby ověření platnosti v {{updateOrCreate()}}:", "dd63416d9b7d9fa4181e89efd619dfd8": "Hodnota není {{array}} nebo {{object}} se sekvenčními číselnými indexy", "ddf0aa14803f1c84f4a97f3803f7471c": "Je vyžadován název třídy", "e08ab0e1ab55f26c357061447b635905": "Nebyl nalezen žádný vztah v {0} pro ({1}. {2}, {3}. {4})", "e0e9504e137a3c3339144b51ed76fef2": "Konektor ne<PERSON><PERSON> def<PERSON> spr<PERSON>: měl by <PERSON><PERSON><PERSON><PERSON><PERSON>{{connector}}` zdroje dat", "e2f282cbe3efba001d6d3a09f7f6ca8c": "Vztah {{polymorphic}} {0}: {1} vyžaduje parametr `polymorphic.foreignKey`, k<PERSON><PERSON> je dodán parametr `polymorphic.discriminator`", "e39e0f5d52bfbf511e645d19ecadd2fa": "Vlastnost {0} má neplatnou klauzuli {1}: {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "<PERSON>ezná<PERSON> \"{0}\" {{id}} \"{1}\".", "e54d944c2a2c85a23caa86027ae307cf": "<PERSON><PERSON><PERSON>grovat modely, k<PERSON><PERSON> nej<PERSON> připoje<PERSON> k tomuto zdroji dat: {0}", "e54f118a75e15e132f16b985274eb46d": "Filtr dotazu {0} není {{object}}", "e55937649d8d7a11706b8cec22d02eae": "Vztah {{<PERSON><PERSON><PERSON>}} {0} je <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "e6161ae8459c79d810e2aa9d21282a39": "Při aktualizaci atributů musíte poskytnout {{id}}!", "eb56c2b0c30cf006e2df00a549ec9c2c": "<PERSON><PERSON><PERSON> \"{0}\" nen<PERSON> definován pro model {1}", "ec42dca074f1818c447f7ad16e2d01af": "{0} nen<PERSON> posky<PERSON> připojeným konektorem", "ecb7aa804bf54c682999d20d6436104c": "{{transaction}} nen<PERSON> aktivní: {0}", "f30809cb932b72a66416a709c8531530": "Konektor nepodporuje {{method}} v rámci transakce", "f41bd91dc0f000a79c0bf842f1b7fdf9": "nelze vytvořit seznam z řetězce JSON: {0}", "f6e8c96c93b9c7687d6c172b3695e898": "Vlastnost {{id}} ({0}) nelze aktualizovat z {1} na {2}", "fa9ae17e8e008d0eb0f0421a2972308c": "Vztah {{polymorphic}} {0}: {1} vyžaduje parametr `model`", "fca4d12faff1035d9d0438d73432571b": "<PERSON><PERSON><PERSON><PERSON><PERSON> pro {0}.{1}", "fd3cc89dc67e2d604eaae21bdf41d403": "Nelze najít vztah {0} pro model {1}", "fec8ebda24db46a9d040bf863765cc44": "Operátor {0} má neplatné klau<PERSON>le {1}: {2}"}