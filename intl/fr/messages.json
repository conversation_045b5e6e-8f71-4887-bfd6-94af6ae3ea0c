{"0483a77cf77741504204e5c066597487": "Relation {{polymorphic}} {0} : {1} n'attend pas le paramètre `polymorphic.as` lors de la définition de `foreignKey`/`discriminator` personnalisé ", "09483e03b91c8bd58732a74b3ef1ec13": "Date non valide : {0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany a reçu une cible qui ne contient pas \"{0}\" (requis)", "0b16d3ffc42f91b4b9a4b3b50c41c838": "L'ordre {0} n'est pas valide", "0bd753a8944ad0af85a939bb25273887": "Impossible de faire expirer la clé inconnue {0}", "0c0b867aca0973ba26e887d3337cc4ec": "<PERSON><PERSON><PERSON><PERSON> {{Polymorphic}} non trouvé : `{0}` non défini", "0c4eb8b6c2ff6e51d7e195eee346ced9": "La table '{0}' n'existe pas.", "0ff31abb394afb555df162e74ff1a0a0": "{{id}} ne peut pas être mis à jour depuis {0} vers {1} lorsque {{forceId}} n'a pas la valeur true", "1ae7d3e0be381efb32bfd1ba652f5172": "AVERTISSEMENT : relation {{polymorphic}} {0} : {1} utilise le mot clé `polymorphic.as` qui sera OBSOLETE dans LoopBack.next. Reportez-vous à cette documentation pour découvrir les solutions de remplacement (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "L'imbrication des transactions n'est pas prise en charge ", "21095484501dbff31af6556fa6039182": "Le paramètre {{offset/skip}} {0} n'est pas valide", "280f4550f90e133118955ec6f6f72830": "Le type de discriminateur {0} est indiqué mais il n'existe pas de modèle de ce nom", "28697ec15968a7969211f6d035ba9260": "Relation {{polymorphic}} {0} : {1} n'attend pas le paramètre `model`", "2c4904377a87fdab502118719cc0d266": "{{Transaction}} n'est pas pris en charge", "2c5c8519721f749aab13c2f04f41d611": "La propriété {0} comporte une clause non valide {1} : 2 valeurs étaient attendues, {2} reçue", "2f4af31c144bbfab1bbf479866acd820": "\nAVERTISSEMENT : Le connecteur {{LoopBack}} \"{0}\" n'est pas installé, tout comme les modules suivants :\n\n {1}\n\n<PERSON>ur corriger le problème, exécutez :\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "Avertissement : le modèle {0}, {{strict mode: `throw`}} a été retiré, utilisez {{`strict: true`}} à la place, ce qui renvoie {{`Validation Error`}} pour les propriétés inconnues,", "38dbf42c29a4645238cc3d632e88ebc9": "{{Relation.modelTo}} n'est pas défini pour la relation {0} et il n'est pas {{polymorphic}}", "3cde8cc9bca22c67278b202ab0720106": "Aucune instance avec l'id {0} trouvée pour {1}", "416dfbb7b823f51c9f3800be81060b41": "Aucune instance avec {{id}} {0} trouvée pour {1}", "49b5afd8c6a19ad9c8abeffb2f8114eb": "La méthode BelongsTo \"getAsync()\" est obsolète ; utilisez \"get()\" à la place.", "4c78325cedbb826db3a05bf5df0e8546": "V<PERSON> devez fournir un {{id}} lors du remplacement !", "4e31b1edd10dadb724d83387de0b5062": "Le paramètre {{limit}} {0} n'est pas valide", "514985b2327f061ffb1c932f6b909979": "Le modèle {0} n'est pas défini.", "525c856e65daab43be247e7b5410febd": "Relation {{polymorphic}} {0} : {1} n'attend pas le paramètre `polymorphic.selector` lors de la définition de `foreignKey`/`discriminator` personnalisé ", "5c18ee111dd87540cdb19a2a93b33be9": "La transaction est annulée car le délai d'attente a été dépassé ", "5ec7e6664256f7ea78f4f06dafc7d974": "La transaction n'est pas prête ; attendez que la promesse renvoyée soit résolue ", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "Aucun nom {{id}} {0}", "614e3355647e4127c96256102dc63376": "La propriété {0} comporte une clause non valide {1} : cha<PERSON><PERSON> ou RegExp attendu", "62a2d80c405b7fec5f547c448ab1b6ff": "{{order}} {0} a une direction non valide", "6502a117987610380b9068ef98b1b0ee": "Aucun enregistrement trouvé dans {0} pour ({1}.{2},{3}.{4})", "67c2bf43b5281ab929617423ea8a6f3e": "Le connecteur {0} ne prend pas en charge l'opération {{replaceById}}. IL ne s'agit pas d'un bogue dans LoopBack. Prenez contact avec les auteurs du connecteur, de préférence via les incidents GitHub.", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}} requiert l'argument {{id}}", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}} requiert l'argument {{id}}", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "La relation {{<PERSON><PERSON><PERSON>}} ne peut pas créer plus d'une instance de {0}", "728232e473bf80272c042df2b7e002f4": "Relation {{polymorphic}} {0} : {1} requiert le paramètre `polymorphic.discriminator` si le paramètre `polymorphic.foreignKey` est fourni", "791ab3031a73ede03f7d6299a85e8289": "Expiration du délai de connexion après {0} ms", "7b277018e43d41bc445731092b91547d": "Non connecté", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}} requiert l'argument {{id}}", "7e9530c0399289be0ee601a604be71ff": "La relation {{BelongsTo}} {0} est vide", "7faa840eb6ce11250a141deb42a6c489": "Relation inconnue {{scope}} : {0}", "8091838319a5cc7d6a34af2f2a616ce9": "Le nom de propriété ne doit pas être \"{{constructor}}\" dans le modèle : {0}", "881e4b0cb86ed59549248ee540a9fd10": "Le nom de propriété \"{{constructor}}\" n'est pas autorisé dans les données {0}", "89afd3a9249f5a8d3edda07d84ca049d": "<PERSON><PERSON><PERSON><PERSON> {{Polymorphic}} introuvable : `{0}`", "89bf6d92731fe7bd2146ce8d0bec205c": "Argument non valide ; doit être une chaîne, un littéral {{regex}} ou un objet {{RegExp}}", "8a39126103a157f501affa070367a1b0": "L'instance {0} n'est pas valide. Détails : {1}.", "8c5ab01638c1ac1d58168c6346a8481a": "Indicateurs {{regex}} non valides : {0}", "938401ea4ce48159efa9be1d4a5e8bab": "Les éléments doivent être un tableau : {0}", "9e1f143ee02946324d34da92f71bf74e": "Relation {0} : {1} requiert le paramètre 'model'", "a004f310d315e592843776fab964eaeb": "Les relations {{Polymorphic}} ont besoin d'un modèle exhaustif", "a0cf0e09c26df14283223e84e6a10f00": "Impossible de mettre à jour les attributs. {{Object}} avec {{id}} {0} n'existe pas !", "a2487abefef4259c2131d96cdb8543b1": "Echec de la connexion : {0}\nUn nouvel essai sera effectué pour la demande suivante.", "a25e41a39c60c4702e55d0c3936576a1": "Non-concordance des clés : {0}.{1} : {2}, {3}.{4} : {5}", "a327355560d495454fba2c1aad6bdf09": "Méthode scope inconnue : {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "Exécuter la commande \"{{npm install loopback-datasource-juggler}} {0}\" ", "a829dee089c912e68c18920ba015400c": "AVERTISSEMENT : la propriété {{id}} ne peut pas être modifiée de {0} en {1} pour le modèle :{2} dans le point d'ancrage d'opération {{'loaded'}}", "a984a076c59e451948b2bcf7a393d860": "AVERTISSEMENT : la propriété {{id}} ne peut pas être modifiée de {0} en {1} pour le modèle :{2} dans le point d'ancrage d'opération {{'before save'}}", "ac04cf275b71c1eb89a41cf6bbad7a64": "La méthode HasOne \"getAsync()\" est obsolète ; utilisez \"get()\" à la place.", "b138294f132edfe1eb2a8211150c7238": "`undefined` inattendu dans la requête", "b15b20280211ad258d92947f05b6e4a5": "Le connecteur n'a pas été initialisé.", "b278876ec93ef9760f00e83f38ba313d": "La méthode de portée \"getAsync()\" est obsolète ; utilisez \"find()\" à la place.", "ba0fd8106eb54de4d003a844206431fd": "Le point d'ancrage de modèle \"{0}\" est obsolète ; utilisez à la place les points d'ancrage d'opération. {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "La clause where {0} n'est pas un {{object}}", "bdb11cc1c780c9ccac33c316cfdc9d82": "Type non défini pour la propriété {0}.{1}", "bdfb951c8ff7ce0cbc08c06f548fd927": "La valeur est un {{object}} vide", "bec226891a505828bfc76c5cfd73b336": "Impossible d'obtenir TTL pour la clé inconnue {0}", "cd930369e86cdd222f7bd117c6f9fa94": "Fournisseur de valeur par défaut inconnu {0}", "cfee4d8149316d9a647c0885cf3cafaf": "Les noms de propriété contenant un ou plusieurs points ne sont pas pris en charge. Modèle : {0}, propriété dynamique : {1}", "d40328eabd8756d795bcdd49d782d4e9": "La source de données ne prend pas en charge les transactions ", "da02dd6c53d4148320eeb31718a7aebe": "Type non valide pour la propriété {0}", "da751a8a748adbde5b55fa83b707b4e2": "Les noms de propriété contenant un ou plusieurs points ne sont pas pris en charge. Modèle : {0}, proprié<PERSON> : {1}", "db03083e9a768388fdbee865249ac67a": "Erreurs de validation ignorées dans {{updateOrCreate()}} :", "dd63416d9b7d9fa4181e89efd619dfd8": "La valeur n'est pas {{array}} ou {{object}} avec des indices numériques séquentiels", "ddf0aa14803f1c84f4a97f3803f7471c": "Nom de classe obligatoire", "e08ab0e1ab55f26c357061447b635905": "Aucune relation trouvée dans {0} pour ({1}.{2},{3}.{4})", "e0e9504e137a3c3339144b51ed76fef2": "Le connecteur n'est pas défini correctement ; il devrait créer le membre `{{connector}}` de la source de données", "e2f282cbe3efba001d6d3a09f7f6ca8c": "Relation {{polymorphic}} {0} : {1} requiert le paramètre `polymorphic.foreignKey` si le paramètre `polymorphic.discriminator` est fourni", "e39e0f5d52bfbf511e645d19ecadd2fa": "La propriété {0} comporte une clause non valide {1} : {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "\"{0}\" {{id}} \"{1}\" inconnu.", "e54d944c2a2c85a23caa86027ae307cf": "Impossible de migrer les modèles non associés à cette source de données : {0}", "e54f118a75e15e132f16b985274eb46d": "Le filtre de requête {0} n'est pas un {{object}}", "e55937649d8d7a11706b8cec22d02eae": "La relation {{<PERSON><PERSON><PERSON>}} {0} est vide", "e6161ae8459c79d810e2aa9d21282a39": "V<PERSON> devez fournir un {{id}} lors de la mise à jour des attributs !", "eb56c2b0c30cf006e2df00a549ec9c2c": "La relation \"{0}\" n'est pas définie pour le modèle {1}", "ec42dca074f1818c447f7ad16e2d01af": "{0} n'est pas fourni par le connecteur associé", "ecb7aa804bf54c682999d20d6436104c": "{{transaction}} n'est pas actif : {0}", "f30809cb932b72a66416a709c8531530": "Le connecteur ne prend pas en charge {{method}} dans une transaction", "f41bd91dc0f000a79c0bf842f1b7fdf9": "impossible de créer la liste à partir de la chaîne JSON : {0}", "f6e8c96c93b9c7687d6c172b3695e898": "La propriété {{id}} ({0}) ne peut pas être mise à jour à partir de {1} vers {2}", "fa9ae17e8e008d0eb0f0421a2972308c": "Relation {{polymorphic}} {0} : {1} requiert le paramètre `model`", "fca4d12faff1035d9d0438d73432571b": "Entrée en double pour {0}.{1}", "fd3cc89dc67e2d604eaae21bdf41d403": "Relation {0} introuvable pour le modèle {1}", "fec8ebda24db46a9d040bf863765cc44": "L'opérateur {0} comporte des clauses non valides {1} : {2}"}