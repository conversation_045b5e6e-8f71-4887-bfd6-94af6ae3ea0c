{"0483a77cf77741504204e5c066597487": "{{polymorphic}} {0} relation: {1} does not expect param `polymorphic.as` when defing custom `foreignKey`/`discriminator` ", "09483e03b91c8bd58732a74b3ef1ec13": "Invalid date: {0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany received target that doesn't contain required \"{0}\"", "0b16d3ffc42f91b4b9a4b3b50c41c838": "The order {0} is not valid", "0bd753a8944ad0af85a939bb25273887": "Cannot expire unknown key {0}", "0c0b867aca0973ba26e887d3337cc4ec": "{{Polymorphic}} model not found: `{0}` not set", "0c4eb8b6c2ff6e51d7e195eee346ced9": "Table '{0}' does not exist.", "0ff31abb394afb555df162e74ff1a0a0": "{{id}} cannot be updated from {0} to {1} when {{forceId}} is set to true", "1ae7d3e0be381efb32bfd1ba652f5172": "WARNING: {{polymorphic}} {0} relation: {1} uses keyword `polymorphic.as` which will be DEPRECATED in LoopBack.next, refer to this doc for replacement solutions (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "Nesting transactions is not supported", "21095484501dbff31af6556fa6039182": "The {{offset/skip}} parameter {0} is not valid", "280f4550f90e133118955ec6f6f72830": "Discriminator type {0} specified but no model exists with such name", "28697ec15968a7969211f6d035ba9260": "{{polymorphic}} {0} relation: {1} does not expect param `model`", "2c4904377a87fdab502118719cc0d266": "{{Transaction}} is not supported", "2c5c8519721f749aab13c2f04f41d611": "The {0} property has invalid clause {1}: Expected precisely 2 values, received {2}", "2f4af31c144bbfab1bbf479866acd820": "\nWARNING: {{<PERSON><PERSON><PERSON>}} connector \"{0}\" is not installed as any of the following modules:\n\n {1}\n\nTo fix, run:\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "Warning: Model {0}, {{strict mode: `throw`}} has been removed, please use {{`strict: true`}} instead, which returns{{`Validation Error`}} for unknown properties,", "********************************": "{{Relation.modelTo}} is not defined for relation {0} and is no {{polymorphic}}", "3cde8cc9bca22c67278b202ab0720106": "No instance with id {0} found for {1}", "416dfbb7b823f51c9f3800be81060b41": "No instance with {{id}} {0} found for {1}", "49b5afd8c6a19ad9c8abeffb2f8114eb": "BelongsTo method \"getAsync()\" is deprecated, use \"get()\" instead.", "4c78325cedbb826db3a05bf5df0e8546": "You must provide an {{id}} when replacing!", "4e31b1edd10dadb724d83387de0b5062": "The {{limit}} parameter {0} is not valid", "514985b2327f061ffb1c932f6b909979": "Model {0} is not defined.", "525c856e65daab43be247e7b5410febd": "{{polymorphic}} {0} relation: {1} does not expect param `polymorphic.selector` when defing custom `foreignKey`/`discriminator` ", "5c18ee111dd87540cdb19a2a93b33be9": "Transaction is rolled back due to timeout", "5ec7e6664256f7ea78f4f06dafc7d974": "Transaction is not ready, wait for the returned promise to resolve", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "No {{id}} name {0}", "614e3355647e4127c96256102dc63376": "The {0} property has invalid clause {1}: Expected a string or RegExp", "62a2d80c405b7fec5f547c448ab1b6ff": "The {{order}} {0} has invalid direction", "6502a117987610380b9068ef98b1b0ee": "No record found in {0} for ({1}.{2} ,{3}.{4})", "67c2bf43b5281ab929617423ea8a6f3e": "The connector {0} does not support {{replaceById}} operation. This is not a bug in LoopBack. Please contact the authors of the connector, preferably via GitHub issues.", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}} requires the {{id}} argument", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}} requires the {{id}} argument", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "{{<PERSON><PERSON><PERSON>}} relation cannot create more than one instance of {0}", "728232e473bf80272c042df2b7e002f4": "{{polymorphic}} {0} relation: {1} requires param `polymorphic.discriminator` when param `polymorphic.foreignKey` is provided", "791ab3031a73ede03f7d6299a85e8289": "Timeout in connecting after {0} ms", "7b277018e43d41bc445731092b91547d": "Not connected", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}} requires the {{id}} argument", "7e9530c0399289be0ee601a604be71ff": "{{BelongsTo}} relation {0} is empty", "7faa840eb6ce11250a141deb42a6c489": "Unknown relation {{scope}}: {0}", "8091838319a5cc7d6a34af2f2a616ce9": "Property name should not be \"{{constructor}}\" in Model: {0}", "881e4b0cb86ed59549248ee540a9fd10": "Property name \"{{constructor}}\" is not allowed in {0} data", "89afd3a9249f5a8d3edda07d84ca049d": "{{Polymorphic}} model not found: `{0}`", "89bf6d92731fe7bd2146ce8d0bec205c": "Invalid argument, must be a string, {{regex}} literal, or {{RegExp}} object", "8a39126103a157f501affa070367a1b0": "The {0} instance is not valid. Details: {1}.", "8c5ab01638c1ac1d58168c6346a8481a": "Invalid {{regex}} flags: {0}", "938401ea4ce48159efa9be1d4a5e8bab": "Items must be an array: {0}", "9e1f143ee02946324d34da92f71bf74e": "{0} relation: {1} requires param `model`", "a004f310d315e592843776fab964eaeb": "{{Polymorphic}} relations need a through model", "a0cf0e09c26df14283223e84e6a10f00": "Could not update attributes. {{Object}} with {{id}} {0} does not exist!", "a2487abefef4259c2131d96cdb8543b1": "Connection fails: {0}\nIt will be retried for the next request.", "a25e41a39c60c4702e55d0c3936576a1": "Key mismatch: {0}.{1}: {2}, {3}.{4}: {5}", "a327355560d495454fba2c1aad6bdf09": "Unknown scope method: {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "Run \"{{npm install loopback-datasource-juggler}} {0}\" command ", "a829dee089c912e68c18920ba015400c": "WARNING: {{id}} property cannot be changed from {0} to {1} for model:{2} in {{'loaded'}} operation hook", "a984a076c59e451948b2bcf7a393d860": "WARNING: {{id}} property cannot be changed from {0} to {1} for model:{2} in {{'before save'}} operation hook", "ac04cf275b71c1eb89a41cf6bbad7a64": "HasOne method \"getAsync()\" is deprecated, use \"get()\" instead.", "b138294f132edfe1eb2a8211150c7238": "Unexpected `undefined` in query", "b15b20280211ad258d92947f05b6e4a5": "The connector has not been initialized.", "b278876ec93ef9760f00e83f38ba313d": "Scope method \"getAsync()\" is deprecated, use \"find()\" instead.", "ba0fd8106eb54de4d003a844206431fd": "Model hook \"{0}\" is deprecated, use Operation hooks instead. {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "The where clause {0} is not an {{object}}", "bdb11cc1c780c9ccac33c316cfdc9d82": "Type not defined for property {0}.{1}", "bdfb951c8ff7ce0cbc08c06f548fd927": "Value is an empty {{object}}", "bec226891a505828bfc76c5cfd73b336": "Cannot get TTL for unknown key {0}", "cd930369e86cdd222f7bd117c6f9fa94": "Unknown default value provider {0}", "cfee4d8149316d9a647c0885cf3cafaf": "Property names containing dot(s) are not supported. Model: {0}, dynamic property: {1}", "d40328eabd8756d795bcdd49d782d4e9": "DataSource does not support transactions", "da02dd6c53d4148320eeb31718a7aebe": "Invalid type for property {0}", "da751a8a748adbde5b55fa83b707b4e2": "Property names containing dot(s) are not supported. Model: {0}, property: {1}", "db03083e9a768388fdbee865249ac67a": "Ignoring validation errors in {{updateOrCreate()}}:", "dd63416d9b7d9fa4181e89efd619dfd8": "Value is not an {{array}} or {{object}} with sequential numeric indices", "ddf0aa14803f1c84f4a97f3803f7471c": "Class name required", "e08ab0e1ab55f26c357061447b635905": "No relation found in {0} for ({1}.{2},{3}.{4})", "e0e9504e137a3c3339144b51ed76fef2": "Connector is not defined correctly: it should create `{{connector}}` member of dataSource", "e2f282cbe3efba001d6d3a09f7f6ca8c": "{{polymorphic}} {0} relation: {1} requires param `polymorphic.foreignKey` when param `polymorphic.discriminator` is provided", "e39e0f5d52bfbf511e645d19ecadd2fa": "The {0} property has invalid clause {1}: {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "Unknown \"{0}\" {{id}} \"{1}\".", "e54d944c2a2c85a23caa86027ae307cf": "Cannot migrate models not attached to this datasource: {0}", "e54f118a75e15e132f16b985274eb46d": "The query filter {0} is not an {{object}}", "e55937649d8d7a11706b8cec22d02eae": "{{<PERSON><PERSON><PERSON>}} relation {0} is empty", "e6161ae8459c79d810e2aa9d21282a39": "You must provide an {{id}} when updating attributes!", "eb56c2b0c30cf006e2df00a549ec9c2c": "Relation \"{0}\" is not defined for {1} model", "ec42dca074f1818c447f7ad16e2d01af": "{0} is not provided by the attached connector", "ecb7aa804bf54c682999d20d6436104c": "The {{transaction}} is not active: {0}", "f30809cb932b72a66416a709c8531530": "The connector does not support {{method}} within a transaction", "f41bd91dc0f000a79c0bf842f1b7fdf9": "could not create List from JSON string: {0}", "f6e8c96c93b9c7687d6c172b3695e898": "{{id}} property ({0}) cannot be updated from {1} to {2}", "fa9ae17e8e008d0eb0f0421a2972308c": "{{polymorphic}} {0} relation: {1} requires param `model`", "fca4d12faff1035d9d0438d73432571b": "Duplicate entry for {0}.{1}", "fd3cc89dc67e2d604eaae21bdf41d403": "Could not find relation {0} for model {1}", "fec8ebda24db46a9d040bf863765cc44": "The {0} operator has invalid clauses {1}: {2}"}