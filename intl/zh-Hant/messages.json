{"0483a77cf77741504204e5c066597487": "{{polymorphic}} {0} 關係：定義自訂 'foreignKey'/'discriminator' 時 {1} 並未預期參數 'polymorphic.as'", "09483e03b91c8bd58732a74b3ef1ec13": "無效日期：{0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany 收到的目標不含必要的 \"{0}\"", "0b16d3ffc42f91b4b9a4b3b50c41c838": "順序 {0} 無效", "0bd753a8944ad0af85a939bb25273887": "無法使不明金鑰 {0} 到期", "0c0b867aca0973ba26e887d3337cc4ec": "找不到 {{Polymorphic}} 模型：`{0}` 未設定", "0c4eb8b6c2ff6e51d7e195eee346ced9": "表格 '{0}' 不存在。", "0ff31abb394afb555df162e74ff1a0a0": "當 {{forceId}} 設定為 true 時，無法將 {{id}} 從 {0} 更新為 {1}", "1ae7d3e0be381efb32bfd1ba652f5172": "警告：{{polymorphic}} {0} 關係：{1} 使用關鍵字 'polymorphic.as'（將在 LoopBack.next 中淘汰），請參閱此文件以取得取代解決方案 (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "不支援巢狀交易", "21095484501dbff31af6556fa6039182": "{{offset/skip}} 參數 {0} 無效", "280f4550f90e133118955ec6f6f72830": "已指定鑑別器類型 {0}，但不存在此名稱的模型", "28697ec15968a7969211f6d035ba9260": "{{polymorphic}} {0} 關係：{1} 並未預期參數 'model'", "2c4904377a87fdab502118719cc0d266": "不支援 {{Transaction}}", "2c5c8519721f749aab13c2f04f41d611": "{0} 內容具有無效的子句 {1}：預期為正好 2 個值，但接收到 {2} 個", "2f4af31c144bbfab1bbf479866acd820": "\n警告：{{LoopBack}} 連接器 \"{0}\" 未安裝為下列任一模組：\n\n {1}\n\n若要修正，請執行：\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "警告：模型 {0} {{strict mode: `throw`}} 已被移除，請改為使用 {{`strict: true`}}，其會對不明內容傳回 {{`Validation Error`}}，", "38dbf42c29a4645238cc3d632e88ebc9": "{{Relation.modelTo}} 未定義給關係 {0}，而且不是 {{polymorphic}}", "3cde8cc9bca22c67278b202ab0720106": "對於 {1}，找不到 id 為 {0} 的實例", "416dfbb7b823f51c9f3800be81060b41": "對於 {1}，找不到 {{id}} 為 {0} 的實例", "49b5afd8c6a19ad9c8abeffb2f8114eb": "BelongsTo 方法 \"getAsync()\" 已淘汰，請改用 \"get()\"。", "4c78325cedbb826db3a05bf5df0e8546": "取代時必須提供 {{id}}！", "4e31b1edd10dadb724d83387de0b5062": "{{limit}} 參數 {0} 無效", "514985b2327f061ffb1c932f6b909979": "模型 {0} 未定義。", "525c856e65daab43be247e7b5410febd": "{{polymorphic}} {0} 關係：定義自訂 'foreignKey'/'discriminator' 時 {1} 並未預期參數 'polymorphic.selector'", "5c18ee111dd87540cdb19a2a93b33be9": "由於逾時，已回復交易", "5ec7e6664256f7ea78f4f06dafc7d974": "交易尚未備妥，等候解析傳回的承諾", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "沒有 {{id}} 名稱 {0}", "614e3355647e4127c96256102dc63376": "{0} 內容具有無效的子句 {1}：預期為字串或 RegExp", "62a2d80c405b7fec5f547c448ab1b6ff": "{{order}} {0} 的方向無效", "6502a117987610380b9068ef98b1b0ee": "在 {0} 中找不到 ({1}.{2}，{3}.{4}) 的記錄", "67c2bf43b5281ab929617423ea8a6f3e": "連接器 {0} 不支援 {{replaceById}} 作業。這不是 LoopBack 的錯誤。請聯絡連接器的作者，建議透過 GitHub 問題聯絡。", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}} 需要 {{id}} 引數", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}} 需要 {{id}} 引數", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "{{HasOne}} 關係無法建立多個 {0} 實例", "728232e473bf80272c042df2b7e002f4": "{{polymorphic}} {0} 關係：提供參數 'polymorphic.foreignKey' 時 {1} 需要參數 'polymorphic.discriminator'", "791ab3031a73ede03f7d6299a85e8289": "連接 {0} 毫秒之後逾時", "7b277018e43d41bc445731092b91547d": "未連接", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}} 需要 {{id}} 引數", "7e9530c0399289be0ee601a604be71ff": "{{BelongsTo}} 關係 {0} 是空的", "7faa840eb6ce11250a141deb42a6c489": "不明關係 {{scope}}：{0}", "8091838319a5cc7d6a34af2f2a616ce9": "在模型中，內容名稱不應該為 \"{{constructor}}\"：{0}", "881e4b0cb86ed59549248ee540a9fd10": "{0} 資料中不接受內容名稱 \"{{constructor}}\"", "89afd3a9249f5a8d3edda07d84ca049d": "找不到 {{Polymorphic}} 模型：`{0}`", "89bf6d92731fe7bd2146ce8d0bec205c": "引數無效，必須是字串、{{regex}} 文字或 {{RegExp}} 物件", "8a39126103a157f501affa070367a1b0": "{0} 實例無效。詳細資料：{1}。", "8c5ab01638c1ac1d58168c6346a8481a": "無效 {{regex}} 旗標：{0}", "938401ea4ce48159efa9be1d4a5e8bab": "項目必須是陣列：{0}", "9e1f143ee02946324d34da92f71bf74e": "{0} 關係：{1} 需要參數 'model'", "a004f310d315e592843776fab964eaeb": "{{Polymorphic}} 關係需要有通過模型", "a0cf0e09c26df14283223e84e6a10f00": "無法更新屬性。{{id}} 為 {0} 的 {{Object}} 不存在！", "a2487abefef4259c2131d96cdb8543b1": "連線失敗：{0}\n將在下一次要求時重試。", "a25e41a39c60c4702e55d0c3936576a1": "金鑰不符：{0}.{1}：{2}，{3}.{4}：{5}", "a327355560d495454fba2c1aad6bdf09": "不明範圍方法：{0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "執行 \"{{npm install loopback-datasource-juggler}} {0}\" 指令", "a829dee089c912e68c18920ba015400c": "警告：在 {{'loaded'}} 作業連結鉤中，無法將 model:{2} 的 {{id}} 內容從 {0} 變更為 {1}", "a984a076c59e451948b2bcf7a393d860": "警告：在 {{'before save'}} 作業連結鉤中，無法將 model:{2} 的 {{id}} 內容從 {0} 變更為 {1}", "ac04cf275b71c1eb89a41cf6bbad7a64": "Has<PERSON><PERSON> 方法 \"getAsync()\" 已淘汰，請改用 \"get()\"。", "b138294f132edfe1eb2a8211150c7238": "查詢中有非預期的 `undefined`", "b15b20280211ad258d92947f05b6e4a5": "尚未起始設定連接器。", "b278876ec93ef9760f00e83f38ba313d": "Scope 方法 \"getAsync()\" 已淘汰，請改用 \"find()\"。", "ba0fd8106eb54de4d003a844206431fd": "模型連結鉤 \"{0}\" 已淘汰，請改用作業連結鉤。{{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "where 子句 {0} 不是 {{object}}", "bdb11cc1c780c9ccac33c316cfdc9d82": "未針對內容 {0}.{1} 定義類型", "bdfb951c8ff7ce0cbc08c06f548fd927": "值是空的 {{object}}", "bec226891a505828bfc76c5cfd73b336": "無法取得不明金鑰 {0} 的 TTL", "cd930369e86cdd222f7bd117c6f9fa94": "預設值提供者 {0} 不明", "cfee4d8149316d9a647c0885cf3cafaf": "不支援含有點的內容名稱。模型：{0}，動態內容：{1}", "d40328eabd8756d795bcdd49d782d4e9": "資料來源不支援交易", "da02dd6c53d4148320eeb31718a7aebe": "內容 {0} 的類型無效", "da751a8a748adbde5b55fa83b707b4e2": "不支援含有點的內容名稱。模型：{0}，內容：{1}", "db03083e9a768388fdbee865249ac67a": "忽略 {{updateOrCreate()}} 中的驗證錯誤：", "dd63416d9b7d9fa4181e89efd619dfd8": "值不是具有循序數值索引的 {{array}} 或 {{object}}", "ddf0aa14803f1c84f4a97f3803f7471c": "需要類別名稱", "e08ab0e1ab55f26c357061447b635905": "在 {0} 中找不到 ({1}.{2}，{3}.{4}) 的關係", "e0e9504e137a3c3339144b51ed76fef2": "未正確定義連接器：應該建立資料來源的 `{{connector}}` 成員", "e2f282cbe3efba001d6d3a09f7f6ca8c": "{{polymorphic}} {0} 關係：提供參數 'polymorphic.discriminator' 時 {1} 需要參數 'polymorphic.foreignKey'", "e39e0f5d52bfbf511e645d19ecadd2fa": "{0} 內容具有無效的子句 {1}：{2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "\"{0}\" {{id}} \"{1}\" 不明。", "e54d944c2a2c85a23caa86027ae307cf": "無法移轉未連接至這個資料來源的模型：{0}", "e54f118a75e15e132f16b985274eb46d": "查詢過濾器 {0} 不是 {{object}}", "e55937649d8d7a11706b8cec22d02eae": "{{HasOne}} 關係 {0} 是空的", "e6161ae8459c79d810e2aa9d21282a39": "更新屬性時必須提供 {{id}}！", "eb56c2b0c30cf006e2df00a549ec9c2c": "未定義 {1} 模型的關係 \"{0}\"", "ec42dca074f1818c447f7ad16e2d01af": "連接的連接器未提供 {0}", "ecb7aa804bf54c682999d20d6436104c": "{{transaction}} 為非作用中：{0}", "f30809cb932b72a66416a709c8531530": "連接器在交易內不支援 {{method}}", "f41bd91dc0f000a79c0bf842f1b7fdf9": "無法從 JSON 字串建立 List：{0}", "f6e8c96c93b9c7687d6c172b3695e898": "無法將 {{id}} 內容 ({0}) 從 {1} 更新為 {2}", "fa9ae17e8e008d0eb0f0421a2972308c": "{{polymorphic}} {0} 關係：{1} 需要參數 'model'", "fca4d12faff1035d9d0438d73432571b": "{0}.{1} 的重複項目", "fd3cc89dc67e2d604eaae21bdf41d403": "找不到模型 {1} 的關係 {0}", "fec8ebda24db46a9d040bf863765cc44": "{0} 運算子具有無效的子句 {1}：{2}"}