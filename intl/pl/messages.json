{"0483a77cf77741504204e5c066597487": "Rela<PERSON><PERSON> {{polymorphic}} {0}: {1} nie oczekuje parametru `polymorphic.as` podczas definiowania niestandardowego obiektu `foreignKey`/`discriminator` ", "09483e03b91c8bd58732a74b3ef1ec13": "Niepoprawna data: {0}", "0a5aa17f7866a85e3aee37ef5369403c": "Funkcja LinkManyToMany otrzymała element docelowy, kt<PERSON><PERSON> nie zawiera wymaganej wartości \"{0}\"", "0b16d3ffc42f91b4b9a4b3b50c41c838": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0} jest ni<PERSON><PERSON><PERSON><PERSON>", "0bd753a8944ad0af85a939bb25273887": "Nie można unieważnić nieznanego klucza {0}", "0c0b867aca0973ba26e887d3337cc4ec": "Model {{Polymorphic}} nie został znaleziony: nie ustawi<PERSON> w<PERSON> `{0}`", "0c4eb8b6c2ff6e51d7e195eee346ced9": "<PERSON><PERSON><PERSON> '{0}' nie ist<PERSON>.", "0ff31abb394afb555df162e74ff1a0a0": "{{id}} nie może by<PERSON> z<PERSON>alizowany z {0} na {1}, g<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{forceId}} ma warto<PERSON>ć true", "1ae7d3e0be381efb32bfd1ba652f5172": "OSTRZEŻENIE: <PERSON><PERSON><PERSON><PERSON> {{polymorphic}} {0}: {1} używa słowa kluczowego `polymorphic.as`, które będzie NIEAKTUALNE w aplikacji LoopBack.next; aby uzyskać informacje o rozwiązaniach zastępczych, zapoznaj się z tym dokumentem (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "Zagnieżdżanie transakcji nie jest obsługiwane", "21095484501dbff31af6556fa6039182": "Parametr {{offset/skip}} {0} jest niepoprawny", "280f4550f90e133118955ec6f6f72830": "Określono typ wyróżnika {0}, ale nie istnieje model o takiej nazwie", "28697ec15968a7969211f6d035ba9260": "<PERSON><PERSON><PERSON><PERSON> {{polymorphic}} {0}: {1} nie o<PERSON><PERSON> parametru `model`", "2c4904377a87fdab502118719cc0d266": "{{Transaction}} nie jest obsługiwana", "2c5c8519721f749aab13c2f04f41d611": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0} ma niepoprawną klauzulę {1}: oczekiwano dokładnie 2 war<PERSON>ś<PERSON>, o<PERSON><PERSON><PERSON><PERSON> {2}", "2f4af31c144bbfab1bbf479866acd820": "\nOSTRZEŻENIE: Konektor {{LoopBack}} \"{0}\" nie jest zainstalowany jako żaden z następujących modułów:\n\n {1}\n\nAby to <PERSON><PERSON><PERSON>, uruchom komendę:\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "Ostrzeżenie: model {0}, {{strict mode: `throw`}}, z<PERSON><PERSON><PERSON>, zamiast niego użyj modelu {{`strict: true`}}, kt<PERSON>ry zwraca {{`Validation Error`}} dla niez<PERSON>,", "38dbf42c29a4645238cc3d632e88ebc9": "Model {{Relation.modelTo}} nie został zdefiniowany dla relacji {0} i nie jest {{polymorphic}}", "3cde8cc9bca22c67278b202ab0720106": "Nie znaleziono instancji o identyfikatorze {0} dla {1}", "416dfbb7b823f51c9f3800be81060b41": "Nie znaleziono instancji o identyfikatorze {{id}} {0} dla {1}", "49b5afd8c6a19ad9c8abeffb2f8114eb": "<PERSON><PERSON> BelongsTo \"getAsync()\" jest ni<PERSON><PERSON><PERSON><PERSON>, zamiast niej uż<PERSON>j metody \"get()\".", "4c78325cedbb826db3a05bf5df0e8546": "Podczas zastępowania należy podać {{id}}!", "4e31b1edd10dadb724d83387de0b5062": "Parametr {{limit}} {0} jest niepoprawny", "514985b2327f061ffb1c932f6b909979": "Model {0} nie jest z<PERSON><PERSON><PERSON><PERSON>y.", "525c856e65daab43be247e7b5410febd": "Rela<PERSON><PERSON> {{polymorphic}} {0}: {1} nie oczekuje parametru `polymorphic.selector` podczas definiowania niestandardowego obiektu `foreignKey`/`discriminator` ", "5c18ee111dd87540cdb19a2a93b33be9": "Transakcja została wycofana z powodu przekroczenia limitu czasu", "5ec7e6664256f7ea78f4f06dafc7d974": "Transakcja nie jest gotowa, poczekaj na rozstrzygnięcie zwróconej obietnicy", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "Brak nazwy {{id}} {0}", "614e3355647e4127c96256102dc63376": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0} ma niepoprawną klauzulę {1}: oczekiwano łańcucha lub wyrażenia regularnego", "62a2d80c405b7fec5f547c448ab1b6ff": "{{order}} {0} ma niepoprawny kierunek", "6502a117987610380b9068ef98b1b0ee": "<PERSON>e znaleziono rekordu w {0} dla ({1}.{2}, {3}.{4})", "67c2bf43b5281ab929617423ea8a6f3e": "Konektor {0} nie obsługuje operacji {{replaceById}}. To nie jest błąd aplikacji LoopBack. Skontaktuj się z autorami konektora, najlepiej za pośrednictwem sekcji serwisu GitHub poświęconej problemom.", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}} wymaga argumentu {{id}}", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}} wymaga argumentu {{id}}", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "<PERSON><PERSON><PERSON><PERSON> {{Has<PERSON>ne}} nie może tworzy<PERSON> więcej niż jednej instancji elementu {0}", "728232e473bf80272c042df2b7e002f4": "<PERSON><PERSON><PERSON><PERSON> {{polymorphic}} {0}: {1} wymaga parametru `polymorphic.discriminator`, je<PERSON><PERSON> parametr `polymorphic.foreignKey`", "791ab3031a73ede03f7d6299a85e8289": "Przekrocz<PERSON> limit czasu połączenia po {0} ms", "7b277018e43d41bc445731092b91547d": "Ni<PERSON> p<PERSON>łą<PERSON>ono", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}} wymaga argumentu {{id}}", "7e9530c0399289be0ee601a604be71ff": "<PERSON><PERSON><PERSON><PERSON> {{BelongsTo}} {0} jest pusta", "7faa840eb6ce11250a141deb42a6c489": "<PERSON><PERSON><PERSON><PERSON> relacja {{scope}}: {0}", "8091838319a5cc7d6a34af2f2a616ce9": "Naz<PERSON>ą właściwości nie może być \"{{constructor}}\" w modelu: {0}", "881e4b0cb86ed59549248ee540a9fd10": "Na<PERSON><PERSON> w<PERSON> \"{{constructor}}\" nie jest dozwo<PERSON>a w danych {0}", "89afd3a9249f5a8d3edda07d84ca049d": "Model {{Polymorphic}} nie został znaleziony: `{0}`", "89bf6d92731fe7bd2146ce8d0bec205c": "Niepoprawny argument; musi to by<PERSON>, literał {{regex}} lub obiekt {{RegExp}}", "8a39126103a157f501affa070367a1b0": "Instancja {0} nie jest poprawna. Szczegóły: {1}.", "8c5ab01638c1ac1d58168c6346a8481a": "Niepoprawne flagi {{regex}}: {0}", "938401ea4ce48159efa9be1d4a5e8bab": "Elementy muszą być tablicą: {0}", "9e1f143ee02946324d34da92f71bf74e": "<PERSON><PERSON><PERSON><PERSON> {0}: {1} wymaga parametru `model`", "a004f310d315e592843776fab964eaeb": "Rela<PERSON>je {{Polymorphic}} wymagają modelu pośredniego", "a0cf0e09c26df14283223e84e6a10f00": "Nie można zaktualizować atrybutów. Obiekt {{Object}} o identyfikatorze {{id}} {0} nie istnieje!", "a2487abefef4259c2131d96cdb8543b1": "Nawiązanie połączenia nie powiodło się: {0}\nZostanie podjęta ponowna próba wykonania następnego żądania.", "a25e41a39c60c4702e55d0c3936576a1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> klucza: {0}.{1}: {2}, {3}.{4}: {5}", "a327355560d495454fba2c1aad6bdf09": "<PERSON><PERSON><PERSON>a metoda zasięgu: {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "Uruchom komendę \"{{npm install loopback-datasource-juggler}} {0}\"", "a829dee089c912e68c18920ba015400c": "OSTRZEŻENIE: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{id}} nie może zosta<PERSON> zmieniona z {0} na {1} dla modelu: {2} w haku operacji {{'loaded'}}", "a984a076c59e451948b2bcf7a393d860": "OSTRZEŻENIE: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{id}} nie może zosta<PERSON> zmieniona z {0} na {1} dla modelu: {2} w haku operacji {{'before save'}}", "ac04cf275b71c1eb89a41cf6bbad7a64": "<PERSON><PERSON> \"getAsync()\" jest ni<PERSON><PERSON><PERSON><PERSON>, zamiast niej uż<PERSON>j metody \"get()\".", "b138294f132edfe1eb2a8211150c7238": "Nieoczekiwany element `undefined` w zapytaniu", "b15b20280211ad258d92947f05b6e4a5": "Konektor nie został zain<PERSON>wany.", "b278876ec93ef9760f00e83f38ba313d": "<PERSON><PERSON> \"getAsync()\" jest ni<PERSON><PERSON><PERSON><PERSON>, zamiast niej użyj metody \"find()\".", "ba0fd8106eb54de4d003a844206431fd": "Hak modelu \"{0}\" jest nieak<PERSON><PERSON><PERSON>, zamiast niego użyj haków operacji. {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "Klauzula where {0} nie jest obiektem {{object}}", "bdb11cc1c780c9ccac33c316cfdc9d82": "<PERSON><PERSON> zdefiniowano typu dla właściwości {0}.{1}", "bdfb951c8ff7ce0cbc08c06f548fd927": "<PERSON><PERSON><PERSON><PERSON> jest pustym elementem {{object}}", "bec226891a505828bfc76c5cfd73b336": "Nie można uzyskać wartości TTL dla nieznanego klucza {0}", "cd930369e86cdd222f7bd117c6f9fa94": "Nieznany dostawca wartości domyślnych {0}", "cfee4d8149316d9a647c0885cf3cafaf": "Nazwy właściwości zawierające kropki nie są obsługiwane. Model: {0}, w<PERSON><PERSON><PERSON><PERSON>ść dynamiczna: {1}", "d40328eabd8756d795bcdd49d782d4e9": "Źródło danych nie obsługuje transakcji", "da02dd6c53d4148320eeb31718a7aebe": "Niepoprawny typ właściwości {0}", "da751a8a748adbde5b55fa83b707b4e2": "Nazwy właściwości zawierające kropki nie są obsługiwane. Model: {0}, w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: {1}", "db03083e9a768388fdbee865249ac67a": "Ignorowanie błędów sprawdzania poprawności w metodzie {{updateOrCreate()}}:", "dd63416d9b7d9fa4181e89efd619dfd8": "<PERSON><PERSON><PERSON><PERSON> nie jestem elementem {{array}} lub {{object}} z sekwencyjnymi indeksami l<PERSON>i", "ddf0aa14803f1c84f4a97f3803f7471c": "<PERSON><PERSON><PERSON> klasy jest wymagana", "e08ab0e1ab55f26c357061447b635905": "<PERSON>e znaleziono relacji w {0} dla ({1}.{2}.{3}.{4})", "e0e9504e137a3c3339144b51ed76fef2": "Konektor nie został poprawnie zdefiniowany: powinien utworzyć element '{{connector}}' <PERSON><PERSON><PERSON><PERSON><PERSON> danych", "e2f282cbe3efba001d6d3a09f7f6ca8c": "<PERSON><PERSON><PERSON><PERSON> {{polymorphic}} {0}: {1} wymaga parametru `polymorphic.foreignKey`, je<PERSON><PERSON> parametr `polymorphic.discriminator`", "e39e0f5d52bfbf511e645d19ecadd2fa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0} ma niepoprawną klauzulę {1}: {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "<PERSON><PERSON><PERSON>y identyfikator {{id}} \"{0}\" \"{1}\".", "e54d944c2a2c85a23caa86027ae307cf": "Nie można migrować modeli, które nie są przyłączone do tego źródła danych: {0}", "e54f118a75e15e132f16b985274eb46d": "Filtr zapytania {0} nie jest obiektem {{object}}", "e55937649d8d7a11706b8cec22d02eae": "<PERSON><PERSON><PERSON><PERSON> {{<PERSON><PERSON><PERSON>}} {0} jest pusta", "e6161ae8459c79d810e2aa9d21282a39": "Podczas aktualizowania atrybutów należy podać identyfikator {{id}}!", "eb56c2b0c30cf006e2df00a549ec9c2c": "<PERSON><PERSON><PERSON><PERSON> \"{0}\" nie została zdefiniowana dla modelu {1}", "ec42dca074f1818c447f7ad16e2d01af": "Element {0} nie został udostępniony przez przyłączony konektor", "ecb7aa804bf54c682999d20d6436104c": "Transakcja {{transaction}} nie jest aktywna: {0}", "f30809cb932b72a66416a709c8531530": "Metoda {{method}} w transakcji nie jest obsługiwana przez konektor", "f41bd91dc0f000a79c0bf842f1b7fdf9": "nie można utworzyć listy z łańcucha JSON: {0}", "f6e8c96c93b9c7687d6c172b3695e898": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{id}} ({0}) nie może zostać zaktualizowana z {1} na {2}", "fa9ae17e8e008d0eb0f0421a2972308c": "<PERSON><PERSON><PERSON><PERSON> {{polymorphic}} {0}: {1} wymaga parametru `model`", "fca4d12faff1035d9d0438d73432571b": "Zduplikowany wpis dla {0}.{1}", "fd3cc89dc67e2d604eaae21bdf41d403": "<PERSON>e można z<PERSON> relacji {0} dla modelu {1}", "fec8ebda24db46a9d040bf863765cc44": "Operator {0} zawiera niepoprawne klauzule {1}: {2}"}