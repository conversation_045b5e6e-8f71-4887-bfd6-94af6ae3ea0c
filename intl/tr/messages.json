{"0483a77cf77741504204e5c066597487": "{{polymorphic}} {0} il<PERSON><PERSON><PERSON><PERSON>: {1}, <PERSON>zel `foreignKey`/`discriminator` tanımlarken `polymorphic.as` parametresini beklemiyor ", "09483e03b91c8bd58732a74b3ef1ec13": "Geçersiz tarih: {0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany, gere<PERSON><PERSON> \"{0}\" öğesini içermeyen hedef aldı", "0b16d3ffc42f91b4b9a4b3b50c41c838": "{0} sırası geçerli değil", "0bd753a8944ad0af85a939bb25273887": "Bilinmeyen {0} anahtarı süre bitimine uğratılamaz", "0c0b867aca0973ba26e887d3337cc4ec": "{{Polymorphic}} model bulunamadı: `{0}` a<PERSON><PERSON><PERSON> değil", "0c4eb8b6c2ff6e51d7e195eee346ced9": "'{0}' tablosu yok", "0ff31abb394afb555df162e74ff1a0a0": "{{id}}, {{forceId}} true olarak ayarlandığında {0} durumundan {1} durumuna güncellenemiyor", "1ae7d3e0be381efb32bfd1ba652f5172": "UYARI: {{polymorphic}} {0} il<PERSON><PERSON><PERSON><PERSON>: {1}, LoopBack.next içinde KULLANIMDAN KALDIRILACAK `polymorphic.as` an<PERSON><PERSON> s<PERSON>üğü<PERSON>ü kullanıyor; değiştirme çözümleri için bu belgeye bakın (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "İç içe geçmiş işlemler desteklenmiyor", "21095484501dbff31af6556fa6039182": "{{offset/skip}} parametresi {0} ge<PERSON><PERSON><PERSON>", "280f4550f90e133118955ec6f6f72830": "{0} ayrımsayıcı tipi belirtildi, ancak bu adı ta<PERSON>ıyan bir model yok", "28697ec15968a7969211f6d035ba9260": "{{polymorphic}} {0} il<PERSON><PERSON><PERSON><PERSON>: {1}, `model` parametresini beklemiyor", "2c4904377a87fdab502118719cc0d266": "{{Transaction}} desteklenmiyor", "2c5c8519721f749aab13c2f04f41d611": "{0} özelliği geçersiz {1} yantümcesi içeriyor: Tam olarak 2 de<PERSON>er bekleniyor, {2} alındı", "2f4af31c144bbfab1bbf479866acd820": "\nUYARI: {{LoopBack}} bağlayıcısı \"{0}\" şu modüllerden biri olarak kurulmadı:\n\n {1}\n\nDüzeltmek için şu komutu çalıştırın:\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "Uyarı: {0} modeli, {{strict mode: `throw`}} kaldır<PERSON>ld<PERSON>, lütfen bunun yerine bilinmeyen özellikler için {{`Validation Error`}} döndüren {{`strict: true`}} kullanın", "38dbf42c29a4645238cc3d632e88ebc9": "{{Relation.modelTo}}, {0} il<PERSON>ş<PERSON><PERSON> i<PERSON> ve {{polymorphic}} değil", "3cde8cc9bca22c67278b202ab0720106": "{1} için {0} tanıtıcılı bir eşgörünüm bulunamadı", "416dfbb7b823f51c9f3800be81060b41": "{1} için {{id}} {0} tanıtıcılı bir eşgörünüm bulunamadı", "49b5afd8c6a19ad9c8abeffb2f8114eb": "\"getAsync()\" BelongsTo yöntemi kullanımdan kaldırıldı, bunun yerine \"get()\" kullanın.", "4c78325cedbb826db3a05bf5df0e8546": "Değiştirirken bir {{id}} belirtmelisiniz!", "4e31b1edd10dadb724d83387de0b5062": "{{limit}} parametresi {0} g<PERSON><PERSON><PERSON><PERSON>", "514985b2327f061ffb1c932f6b909979": "{0} <PERSON><PERSON>", "525c856e65daab43be247e7b5410febd": "{{polymorphic}} {0} il<PERSON><PERSON><PERSON><PERSON>: {1}, özel `foreignKey`/`discriminator` öğ<PERSON>ni tan<PERSON> `polymorphic.selector` parametresini beklemiyor ", "5c18ee111dd87540cdb19a2a93b33be9": "Zaman aşımı nedeniyle işlem geri alındı", "5ec7e6664256f7ea78f4f06dafc7d974": "İşlem <PERSON>ı<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> döndür<PERSON><PERSON> du<PERSON>u be<PERSON>. ", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "{{id}} adı {0} yok", "614e3355647e4127c96256102dc63376": "{0} özelliğinde geçersiz {1} yantümcesi var: Bir dizgi ya da RegExp bekleniyor", "62a2d80c405b7fec5f547c448ab1b6ff": "{{order}} {0} s<PERSON>rasının yönü geçersiz", "6502a117987610380b9068ef98b1b0ee": "({1}.{2} ,{3}.{4}) için {0} içinde kayıt bulunamadı. ", "67c2bf43b5281ab929617423ea8a6f3e": "{0} bağlayıcısı {{replaceById}} işlemini desteklemiyor. Bu LoopBack'deki bir hata değil. Lütfen, mümkünse GitHub sorunlarıyla birlikte, ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yazar<PERSON><PERSON>na ba<PERSON><PERSON><PERSON>.", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}}, {{id}} bağ<PERSON><PERSON><PERSON>z değişkenini gerektirir", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}}, {{id}} bağ<PERSON><PERSON><PERSON>z değ<PERSON>şkenini gerektirir", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "{{HasOne}} il<PERSON><PERSON><PERSON><PERSON> birden çok {0} eşgörünümü ya<PERSON>z", "728232e473bf80272c042df2b7e002f4": "{{polymorphic}} {0} il<PERSON><PERSON><PERSON><PERSON>: `polymorphic.foreignKey` parametresi sağlandığında {1} için `polymorphic.discriminator` parametresi gerekir", "791ab3031a73ede03f7d6299a85e8289": "{0} mi<PERSON><PERSON><PERSON><PERSON> sonra ba<PERSON><PERSON>ıda zaman aşımı oluştu", "7b277018e43d41bc445731092b91547d": "Bağlı değil", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}}, {{id}} bağ<PERSON><PERSON><PERSON>z değişkenini gerektirir", "7e9530c0399289be0ee601a604be71ff": "{{BelongsTo}} ilişkisi {0} boş", "7faa840eb6ce11250a141deb42a6c489": "Bilinmeyen ilişki {{scope}}: {0}", "8091838319a5cc7d6a34af2f2a616ce9": "Şu modelde özellik adı \"{{constructor}}\" olmamalıdır: {0}", "881e4b0cb86ed59549248ee540a9fd10": "{0} verilerinde \"{{constructor}}\" özellik adına izin verilmez", "89afd3a9249f5a8d3edda07d84ca049d": "{{Polymorphic}} model bulunamadı: `{0}`", "89bf6d92731fe7bd2146ce8d0bec205c": "Bağımsız değişken geçersiz; bir diz<PERSON>, {{regex}} hazır bilgisi ya da {{RegExp}} nesnesi o<PERSON>l<PERSON>ır", "8a39126103a157f501affa070367a1b0": "{0} eşgörünümü geçerli değil. Ayrıntılar: {1}.", "8c5ab01638c1ac1d58168c6346a8481a": "Geçersiz {{regex}} işaretleri: {0}", "938401ea4ce48159efa9be1d4a5e8bab": "Öğeler bir dizi olmalıdır: {0}", "9e1f143ee02946324d34da92f71bf74e": "{0} il<PERSON><PERSON><PERSON><PERSON>: {1} i<PERSON>in `model` parametresi gerekir", "a004f310d315e592843776fab964eaeb": "{{Polymorphic}} ilişkileri için aracı model gerekir", "a0cf0e09c26df14283223e84e6a10f00": "Öznitelikler güncellenemedi. Tanıtıcısı {{id}} olan {0} {{Object}} nesnesi yok!", "a2487abefef4259c2131d96cdb8543b1": "Bağlantı başarısız oldu: {0}\nSonraki istek için yeniden denenecek.", "a25e41a39c60c4702e55d0c3936576a1": "<PERSON><PERSON><PERSON>ğ<PERSON>: {0}.{1}: {2}, {3}.{4}: {5}", "a327355560d495454fba2c1aad6bdf09": "Bilinmeyen kapsam yöntemi: {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "\"{{npm install loopback-datasource-juggler}} {0}\" komutunu çalıştırın ", "a829dee089c912e68c18920ba015400c": "UYARI: {{id}} ö<PERSON>liği, {{'loaded'}} işlem çengelinde {2} modeli için {0} değerinden {1} değ<PERSON>ne çevrilemiyor", "a984a076c59e451948b2bcf7a393d860": "UYARI: {{id}} ö<PERSON>liği, {{'before save'}} işlem çengelinde {2} modeli için {0} değerinden {1} değ<PERSON>ne çevrilemiyor", "ac04cf275b71c1eb89a41cf6bbad7a64": "\"getAsync()\" HasOne yöntemi kullanımdan kaldırıldı, bunun yerine \"get()\" kullanın", "b138294f132edfe1eb2a8211150c7238": "Sorg<PERSON> beklenmeyen `undefined`", "b15b20280211ad258d92947f05b6e4a5": "Bağlayıcı başlatılmamış.", "b278876ec93ef9760f00e83f38ba313d": "\"getAsync()\" kapsam yö<PERSON>mi kullanı<PERSON>dan kaldırıldı, bunun yerine \"find()\" kullanın.", "ba0fd8106eb54de4d003a844206431fd": "\"{0}\" model <PERSON><PERSON><PERSON> kull<PERSON><PERSON> kald<PERSON>ld<PERSON>, onun yerine işlem çengellerini kullanın. {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "Where yantümcesi {0} bir {{object}} değil", "bdb11cc1c780c9ccac33c316cfdc9d82": "{0}.{1} özelliği için tip tanımlanmadı", "bdfb951c8ff7ce0cbc08c06f548fd927": "<PERSON><PERSON><PERSON> bo<PERSON> bir {{object}}", "bec226891a505828bfc76c5cfd73b336": "Bilinmeyen {0} anahtarı için TTL alınamıyor", "cd930369e86cdd222f7bd117c6f9fa94": "Bilinmeyen varsayılan değer sağlayıcısı {0}", "cfee4d8149316d9a647c0885cf3cafaf": "Nokta içeren özellik adları desteklenmez. Model: {0}, dinamik özellik: {1}", "d40328eabd8756d795bcdd49d782d4e9": "DataSource işlemleri desteklemiyor", "da02dd6c53d4148320eeb31718a7aebe": "{0} özelliği için geçersiz tip", "da751a8a748adbde5b55fa83b707b4e2": "Nokta içeren özellik adları desteklenmez. Model: {0}, özellik: {1}", "db03083e9a768388fdbee865249ac67a": "{{updateOrCreate()}} işlemindeki doğrulama hataları yoksayılıyor:", "dd63416d9b7d9fa4181e89efd619dfd8": "<PERSON><PERSON><PERSON>, sı<PERSON><PERSON> sayısal dizeler içeren bir {{array}} ya da {{object}} değil", "ddf0aa14803f1c84f4a97f3803f7471c": "<PERSON>ı<PERSON><PERSON>f adı zorunludur", "e08ab0e1ab55f26c357061447b635905": "({1}.{2} ,{3}.{4}) için {0} içinde ilişki bulunamadı.", "e0e9504e137a3c3339144b51ed76fef2": "Bağlayıcı doğru tanımlanmadı: Veri kaynağının `{{connector}}` üyesini yaratmalıdır", "e2f282cbe3efba001d6d3a09f7f6ca8c": "{{polymorphic}} {0} il<PERSON><PERSON><PERSON><PERSON>: `polymorphic.discriminator` parametresi sağlandığında {1} için `polymorphic.foreignKey` parametresi gerekir", "e39e0f5d52bfbf511e645d19ecadd2fa": "{0} özelliğinde geçersiz {1} yantümcesi var: {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "Bilinmeyen \"{0}\" {{id}} \"{1}\".", "e54d944c2a2c85a23caa86027ae307cf": "Bu veri kayna<PERSON><PERSON>na eklenmemiş modeller geçirilemez: {0}", "e54f118a75e15e132f16b985274eb46d": "<PERSON><PERSON><PERSON> süzgeci {0} bir {{object}} değil", "e55937649d8d7a11706b8cec22d02eae": "{{HasOne}} ilişkisi {0} boş", "e6161ae8459c79d810e2aa9d21282a39": "Öznitelikleri güncellerken bir {{id}} belirtmelisiniz!", "eb56c2b0c30cf006e2df00a549ec9c2c": "{1} <PERSON><PERSON> \"{0}\" iliş<PERSON><PERSON>", "ec42dca074f1818c447f7ad16e2d01af": "Eklenen bağlayıcı tarafından {0} sağlanmadı", "ecb7aa804bf54c682999d20d6436104c": "{{transaction}} <PERSON><PERSON> de<PERSON>: {0}", "f30809cb932b72a66416a709c8531530": "Bağlayıcısı bir işlem içinde {{method}} yöntemini desteklemiyor.", "f41bd91dc0f000a79c0bf842f1b7fdf9": "JSON dizgisinden liste yaratılamadı: {0}", "f6e8c96c93b9c7687d6c172b3695e898": "{{id}} <PERSON><PERSON><PERSON>ği ({0}), {1} değerinden {2} de<PERSON><PERSON>ne güncellenemiyor", "fa9ae17e8e008d0eb0f0421a2972308c": "{{polymorphic}} {0} il<PERSON><PERSON><PERSON><PERSON>: {1} i<PERSON>in `model` parametresi gerekir", "fca4d12faff1035d9d0438d73432571b": "{0}.{1} i<PERSON><PERSON> y<PERSON> g<PERSON>", "fd3cc89dc67e2d604eaae21bdf41d403": "{1} <PERSON><PERSON> i<PERSON> {0} ilişkisi bulunamadı", "fec8ebda24db46a9d040bf863765cc44": "{0} işlecinde geçersiz {1} yantümceleri var: {2}"}