{"0483a77cf77741504204e5c066597487": "{{polymorphic}} {0} Be<PERSON>hung: {1} erwartet keinen Parameter 'polymorphic.as' beim <PERSON> eines angepassten 'foreignKey'/'discriminator' ", "09483e03b91c8bd58732a74b3ef1ec13": "Ungültiges Datum: {0}", "0a5aa17f7866a85e3aee37ef5369403c": "LinkManyToMany hat ein Z<PERSON> empfang<PERSON>, dass das erforderliche \"{0}\" nicht enthält", "0b16d3ffc42f91b4b9a4b3b50c41c838": "Die Reihenfolge {0} ist nicht gültig", "0bd753a8944ad0af85a939bb25273887": "Unbekannter Schlüssel {0} darf nicht ablaufen", "0c0b867aca0973ba26e887d3337cc4ec": "{{Polymorphic}}-<PERSON><PERSON> nicht gefunden: '{0}' nicht festgelegt", "0c4eb8b6c2ff6e51d7e195eee346ced9": "Tabelle '{0}' ist nicht vorhanden.", "0ff31abb394afb555df162e74ff1a0a0": "{{id}} kann nicht von {0} auf {1} aktual<PERSON><PERSON> werden, wenn für {{forceId}} true festgelegt ist", "1ae7d3e0be381efb32bfd1ba652f5172": "WARNUNG: {{polymorphic}} {0} Beziehung: {1} verwendet Schlüsselwort 'polymorphic.as', das in LoopBack.next VERALTET sein wird. Lesen Sie diese Dokumentation zu Ersatzlösungen (https://loopback.io/doc/en/lb3/Polymorphic-relations.html#deprecated-polymorphic-as)", "1daef4e937fe52136597ba8fd2060f55": "Verschachtelungstransaktionen werden nicht unterstützt", "21095484501dbff31af6556fa6039182": "Der {{offset/skip}}-Parameter {0} ist nicht gültig", "280f4550f90e133118955ec6f6f72830": "Diskriminatortyp {0} angegeben, aber es ist kein Modell mit diesem Namen vorhanden", "28697ec15968a7969211f6d035ba9260": "{{polymorphic}} {0} Be<PERSON><PERSON>g: {1} erwart<PERSON> keinen Parameter 'model'", "2c4904377a87fdab502118719cc0d266": "{{Transaction}} wird nicht unterstützt", "2c5c8519721f749aab13c2f04f41d611": "Die Eigenschaft {0} weist die ungültige Klausel {1} auf: Es wurden genau 2 We<PERSON> erwartet, aber {2} empfangen", "2f4af31c144bbfab1bbf479866acd820": "\nWARNUNG: {{LoopBack}}-Konnektor \"{0}\" ist als keines der folgenden Module installiert:\n\n {1}\n\nFühren Sie zur Behebung Folgendes aus:\n\n    {{npm install {2} --save}}\n", "3864f9be10f27723074566d2b3893514": "Warnung: Modell {0}, {{strict mode: `throw`}} wurde entfernt. Verwenden Sie stattdessen {{`strict: true`}}, wodurch {{`Validation Error`}} für unbekannte Eigenschaften zurückgegeben wird.", "********************************": "{{Relation.modelTo}} ist für Beziehung {0} nicht definiert und ist keine {{polymorphic}}", "3cde8cc9bca22c67278b202ab0720106": "<PERSON><PERSON> mit ID {0} für {1} gefunden", "416dfbb7b823f51c9f3800be81060b41": "<PERSON><PERSON> mit {{id}} {0} für {1} gefunden", "49b5afd8c6a19ad9c8abeffb2f8114eb": "BelongsTo-Methode \"getAsync()\" ist veraltet, verwenden Sie stattdessen \"get()\".", "4c78325cedbb826db3a05bf5df0e8546": "<PERSON>e müssen beim <PERSON>en eine {{id}} angeben!", "4e31b1edd10dadb724d83387de0b5062": "Der {{limit}}-Parameter {0} ist nicht gültig", "514985b2327f061ffb1c932f6b909979": "Modell {0} ist nicht definiert.", "525c856e65daab43be247e7b5410febd": "{{polymorphic}} {0} Be<PERSON>hung: {1} erwartet keinen <PERSON> 'polymorphic.selector' be<PERSON> eines angepassten 'foreignKey'/'discriminator' ", "5c18ee111dd87540cdb19a2a93b33be9": "Transaktion wurde aufgrund eines Zeitlimits rückgängig gemacht", "5ec7e6664256f7ea78f4f06dafc7d974": "Transaktion ist nicht bereit, auf die Auflösung des zurückgegebenen Promise warten", "5ec8efeb715a2c34b440f2d76e2cf87d": "  {0}", "6111399276924ffa3bc9a410cdfcb2e5": "Kein {{id}}-Name {0}", "614e3355647e4127c96256102dc63376": "Die Eigenschaft {0} weist die ungültige Klausel {1} auf: Es wurde eine Zeichenfolge oder RegExp erwartet", "62a2d80c405b7fec5f547c448ab1b6ff": "Die {{order}} {0} hat eine ungültige Richtung", "6502a117987610380b9068ef98b1b0ee": "<PERSON><PERSON> gefunden in {0} für ({1}.{2} ,{3}.{4})", "67c2bf43b5281ab929617423ea8a6f3e": "Der Konnektor {0} unterstützt die Operation {{replaceById}} nicht. Hierbei handelt es sich nicht um einen Fehler in LoopBack. Wenden Sie sich an die Ersteller des Konnektors, vorzugsweise über GitHub-Probleme.", "6c3234937d69763fc7f6bcafccc59bbc": "{{Model::deleteById}} erfordert das Argument {{id}}", "6eb6fd4fbd73394000bc25f5776fd20c": "{{Model::exists}} er<PERSON><PERSON> das Argument {{id}}", "6fcc2ff0db7a4f490f5e0ce9e24691f3": "{{HasOne}}-<PERSON><PERSON>hung kann nicht mehr als eine Instanz von {0} erstellen", "728232e473bf80272c042df2b7e002f4": "{{polymorphic}} {0} <PERSON><PERSON><PERSON>g: {1} <PERSON><PERSON><PERSON> den <PERSON> 'polymorphic.discriminator', wenn der <PERSON> 'polymorphic.foreignKey' angegeben ist", "791ab3031a73ede03f7d6299a85e8289": "Zeitlimitüberschreitung bei Verbindungsherstellung nach {0} ms", "7b277018e43d41bc445731092b91547d": "Nicht verbunden", "7bbbdece4eea90e42aa5c0bce295e503": "{{Model::findById}} erfordert das Argument {{id}}", "7e9530c0399289be0ee601a604be71ff": "{{BelongsTo}}-Beziehung {0} ist leer", "7faa840eb6ce11250a141deb42a6c489": "Unbekannter {{scope}} für Beziehung: {0}", "8091838319a5cc7d6a34af2f2a616ce9": "Eigenschaftsname sollte nicht \"{{constructor}}\" sein in Modell: {0}", "881e4b0cb86ed59549248ee540a9fd10": "Eigenschaftsname \"{{constructor}}\" ist in {0}-Daten nicht zulässig", "89afd3a9249f5a8d3edda07d84ca049d": "{{Polymorphic}}-<PERSON>l nicht gefunden: '{0}'", "89bf6d92731fe7bd2146ce8d0bec205c": "Ungültiges Argument, muss eine Zeichenfolge, ein {{regex}}-Literalwert oder ein {{RegExp}}-Objekt sein", "8a39126103a157f501affa070367a1b0": "Die Instanz {0} ist nicht gültig. Details: {1}.", "8c5ab01638c1ac1d58168c6346a8481a": "Ungültige {{regex}}-Flags: {0}", "938401ea4ce48159efa9be1d4a5e8bab": "Elemente müssen ein Array sein: {0}", "9e1f143ee02946324d34da92f71bf74e": "{0} Be<PERSON>hung: {1} er<PERSON>ert den Parameter 'model'", "a004f310d315e592843776fab964eaeb": "{{Polymorphic}}-Beziehungen erfordern ein through-Modell", "a0cf0e09c26df14283223e84e6a10f00": "Attribute konnten nicht aktualisiert werden. {{Object}} mit {{id}} {0} ist nicht vorhanden!", "a2487abefef4259c2131d96cdb8543b1": "Verbindungsfehler: {0}\nNach der nächsten Anforderung findet ein Neuversuch statt.", "a25e41a39c60c4702e55d0c3936576a1": "<PERSON><PERSON><PERSON><PERSON> stimmen nicht überein: {0}.{1}: {2}, {3}.{4}: {5}", "a327355560d495454fba2c1aad6bdf09": "Unbekannte Bereichsmethode: {0}", "a6c18a7f4390cd3d59a2a7a047ae2aab": "<PERSON><PERSON><PERSON> Sie den Befehl \"{{npm install loopback-datasource-juggler}} {0}\" aus ", "a829dee089c912e68c18920ba015400c": "WARNUNG: Eigenschaft {{id}} darf für Modell {2} in Operationshook {{'loaded'}} nicht von {0} in {1} geändert werden", "a984a076c59e451948b2bcf7a393d860": "WARNUNG: Eigenschaft {{id}} darf für Modell {2} in Operationshook {{'before save'}} nicht von {0} in {1} geändert werden", "ac04cf275b71c1eb89a41cf6bbad7a64": "HasOne-Methode \"getAsync()\" ist veraltet, verwenden Sie stattdessen \"get()\".", "b138294f132edfe1eb2a8211150c7238": "Unerwartetes 'nicht definiert' in Abfrage", "b15b20280211ad258d92947f05b6e4a5": "Der Konnektor wurde nicht initialisiert.", "b278876ec93ef9760f00e83f38ba313d": "<PERSON>ope-<PERSON><PERSON> \"getAsync()\" ist veraltet, verwenden Sie stattdessen \"get()\".", "ba0fd8106eb54de4d003a844206431fd": "Modellhook \"{0}\" ist veraltet, verwenden Sie stattdessen Operationshooks. {{http://docs.strongloop.com/display/LB/Operation+hooks}}", "baf2c8b0c5a574b8a894e9b6304fece1": "Bei der where-<PERSON><PERSON> {0} handelt es sich nicht um ein {{object}}", "bdb11cc1c780c9ccac33c316cfdc9d82": "Typ nicht definiert für Eigenschaft {0}.{1}", "bdfb951c8ff7ce0cbc08c06f548fd927": "Wert ist ein leeres {{object}}", "bec226891a505828bfc76c5cfd73b336": "TTL für unbekannten Schlüssel {0} kann nicht abgerufen werden", "cd930369e86cdd222f7bd117c6f9fa94": "Unbekannter Standardwertprovider {0}", "cfee4d8149316d9a647c0885cf3cafaf": "Eigenschaftsnamen, die Punkt(e) enthalten, werden nicht unterstützt. Modell: {0}, dynamische Eigenschaft: {1}", "d40328eabd8756d795bcdd49d782d4e9": "DataSource unterstützt keine Transaktionen", "da02dd6c53d4148320eeb31718a7aebe": "Ungültiger Typ für Eigenschaft {0}", "da751a8a748adbde5b55fa83b707b4e2": "Eigenschaftsnamen, die Punkt(e) enthalten, werden nicht unterstützt. Modell: {0}, Eigenschaft: {1}", "db03083e9a768388fdbee865249ac67a": "Validierungsfehler in {{updateOrCreate()}} werden ignoriert:", "dd63416d9b7d9fa4181e89efd619dfd8": "Der Wert ist kein {{array}} oder {{object}} mit sequenziellen numerischen Indizes", "ddf0aa14803f1c84f4a97f3803f7471c": "Klassenname erford<PERSON>lich", "e08ab0e1ab55f26c357061447b635905": "<PERSON><PERSON> gefunden in {0} für ({1}.{2} ,{3}.{4})", "e0e9504e137a3c3339144b51ed76fef2": "Konnektor ist nicht ordnungsgemäß definiert: Er sollte ein {{connector}}-Member von dataSource erstellen", "e2f282cbe3efba001d6d3a09f7f6ca8c": "{{polymorphic}} {0} <PERSON><PERSON><PERSON>g: {1} <PERSON><PERSON><PERSON> den <PERSON> 'polymorphic.foreignKey', wenn der <PERSON> 'polymorphic.discriminator' angegeben ist", "e39e0f5d52bfbf511e645d19ecadd2fa": "Die Eigenschaft {0} weist die ungültige Klausel {1} auf: {2}", "e4434de4bb8f5a3cd1d416e4d80d7e0b": "Unbekannte \"{0}\" {{id}} \"{1}\".", "e54d944c2a2c85a23caa86027ae307cf": "<PERSON><PERSON>, die nicht an diese Datenquelle angehängt sind, können nicht migriert werden: {0}", "e54f118a75e15e132f16b985274eb46d": "<PERSON><PERSON>gefilter {0} handelt es sich nicht um ein {{object}}", "e55937649d8d7a11706b8cec22d02eae": "{{<PERSON><PERSON><PERSON>}}-Beziehung {0} ist leer", "e6161ae8459c79d810e2aa9d21282a39": "<PERSON>e müssen beim Aktualisieren von Attributen eine {{id}} angeben!", "eb56c2b0c30cf006e2df00a549ec9c2c": "Beziehung \"{0}\" ist nicht definiert für {1} modell", "ec42dca074f1818c447f7ad16e2d01af": "{0} wird vom zugeordneten Konnektor nicht angegeben", "ecb7aa804bf54c682999d20d6436104c": "Die {{transaction}} ist nicht aktiv: {0}", "f30809cb932b72a66416a709c8531530": "Der Konnektor unterstützt {{method}} innerhalb einer Transaktion nicht", "f41bd91dc0f000a79c0bf842f1b7fdf9": "konnte Liste nicht aus JSON-Zeichenfolge erstellen: {0}", "f6e8c96c93b9c7687d6c172b3695e898": "{{id}}-Eigenschaft ({0}) kann nicht von {1} in {2} aktualisiert werden", "fa9ae17e8e008d0eb0f0421a2972308c": "{{polymorphic}} {0} Be<PERSON>hung: {1} er<PERSON><PERSON> den Para<PERSON> 'model'", "fca4d12faff1035d9d0438d73432571b": "<PERSON><PERSON><PERSON> Eintrag für {0}.{1}", "fd3cc89dc67e2d604eaae21bdf41d403": "Beziehung {0} für Modell {1} konnte nicht gefunden werden", "fec8ebda24db46a9d040bf863765cc44": "Der Operator {0} weist ungültige Klauseln {1} auf: {2}"}