name: CI

on:
  push:
    branches: [master]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [master]
  schedule:
    - cron: '0 2 * * 1' # At 02:00 on Monday

permissions: {}

jobs:
  test:
    name: Test
    timeout-minutes: 15
    strategy:
      matrix:
        os: [ubuntu-latest]
        node-version: [18, 20, 22]
        include:
          - os: macos-latest
            node-version: 20
          - os: windows-latest
            node-version: 20
      fail-fast: false
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Update NPM (Node.js v10)
        if: matrix.node-version == 10
        run: npm install --global npm@7
      - name: Update NPM
        if: matrix.node-version != 10
        run: npm install --global npm@8
      - name: Bootstrap project
        run: npm ci --ignore-scripts
      - name: Build project
        run: npm run build
      - name: Run tests
        run: npm test --ignore-scripts
      - name: Generate coverage report
        run: npx --no-install nyc report --reporter=lcov
      - name: Publish coverage report to Coveralls
        uses: coverallsapp/github-action@master
        with:
          flag-name: run-${{ matrix.os }}-node@${{ matrix.node-version }}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          parallel: true

  posttest:
    name: Post-Test
    needs: test
    runs-on: ubuntu-latest
    steps:
    - name: Coveralls finished
      uses: coverallsapp/github-action@master
      with:
        github-token: ${{ secrets.github_token }}
        parallel-finished: true

  code-lint:
    name: Code Lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Use Node.js 20
        uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a # v4.2.0
        with:
          node-version: 20
      - name: Update NPM
        run: npm install --global npm
      - name: Bootstrap project
        run: npm ci --ignore-scripts
      - name: Build project
        run: npm run build
      - name: Verify code linting
        run: npm run lint

  commit-lint:
    name: Commit Lint
    runs-on: ubuntu-latest
    if: ${{ github.event.pull_request }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Use Node.js 20
        uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a # v4.2.0
        with:
          node-version: 20
      - name: Update NPM
        run: npm install --global npm
      - name: Bootstrap project
        run: npm ci --ignore-scripts
      - name: Verify commit linting
        run: npx --no-install commitlint --from origin/master --to HEAD --verbose

  codeql:
    name: CodeQL
    runs-on: ubuntu-latest

    permissions:
      # See: https://github.com/github/codeql-action/blob/008b2cc71c4cf3401f45919d8eede44a65b4a322/README.md#usage
      security-events: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: 'javascript'
        config-file: ./.github/codeql/codeql-config.yaml

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
