---
name: Bug report
about: Create a report to help us improve
labels: bug

---

<!-- 🚨 STOP 🚨 STOP 🚨 STOP 🚨

Are you using LoopBack version 4? Please report the bug here:
https://github.com/strongloop/loopback-next/issues/new

HELP US HELP YOU, PLEASE
- Do a quick search to avoid duplicate issues
- Provide as much information as possible (reproduction sandbox, use case for features, etc.)
- Consider using a more suitable venue for questions such as Stack Overflow, Gitter, etc.

Please fill in the *entire* template below.

-->

## Steps to reproduce

<!-- Describe how to reproduce the issue -->

## Current Behavior

<!-- Describe the observed result -->

## Expected Behavior

<!-- Describe what did you expect instead, what is the desired outcome? -->

## Link to reproduction sandbox

<!--
See https://loopback.io/doc/en/contrib/Reporting-issues.html#loopback-3x-bugs
Note: Failure to provide a sandbox application for reproduction purposes will result in the issue being closed.
-->

## Additional information

<!--
Copy+paste the output of these two commands:
  node -e 'console.log(process.platform, process.arch, process.versions.node)'
  npm ls --prod --depth 0 | grep loopback
-->

## Related Issues

<!-- Did you find other bugs that looked similar? -->

_See [Reporting Issues](http://loopback.io/doc/en/contrib/Reporting-issues.html) for more tips on writing good issues_
