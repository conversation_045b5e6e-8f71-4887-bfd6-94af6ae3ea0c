<!--
Please provide a high-level description of the changes made by your pull request.

Include references to all related GitHub issues and other pull requests, for example:

Fixes #123
Implements #254
See also #23
-->

## Checklist

- [ ] [Sign off your commits](https://loopback.io/doc/en/contrib/code-contrib.html) with <PERSON><PERSON> (Developer Certificate of Origin)
- [ ] `npm test` passes on your machine
- [ ] New tests added or existing tests modified to cover all changes
- [ ] Code conforms with the [style guide](https://loopback.io/doc/en/contrib/style-guide-es6.html)
- [ ] Commit messages are following our [guidelines](https://loopback.io/doc/en/contrib/git-commit-messages.html)
